/**
 * 状态管理系统
 * 提供集中式状态管理和响应式更新
 */

import { StateReducer } from './actions.js';

/**
 * 状态存储类
 */
export class Store {
  constructor() {
    this.state = this.getInitialState();
    this.subscribers = new Map();
    this.middleware = [];
    
    // 绑定方法上下文
    this.setState = this.setState.bind(this);
    this.getState = this.getState.bind(this);
    this.subscribe = this.subscribe.bind(this);
    this.unsubscribe = this.unsubscribe.bind(this);
    this.dispatch = this.dispatch.bind(this);
  }

  /**
   * 获取初始状态
   */
  getInitialState() {
    return {
      // 应用设置
      settings: {
        theme: 'auto',
        fontSize: 'md',
        autoScroll: true,
        showTimestamps: true,
        enableNotifications: true
      },
      
      // 消息列表
      messages: [],
      
      // UI状态
      ui: {
        isLoading: false,
        currentView: 'chat', // 'chat' | 'settings'
        inputValue: '',
        isTyping: false
      },
      
      // 用户状态
      user: {
        id: 'user',
        name: '用户'
      },
      
      // 应用状态
      app: {
        version: '1.0.0',
        initialized: false,
        lastActivity: Date.now()
      }
    };
  }

  /**
   * 设置状态
   * @param {string} key - 状态键
   * @param {any} value - 状态值
   * @param {boolean} silent - 是否静默更新（不触发订阅者）
   */
  setState(key, value, silent = false) {
    const oldValue = this.state[key];
    
    // 运行中间件
    const action = { type: 'SET_STATE', key, value, oldValue };
    const processedAction = this.runMiddleware(action);
    
    if (processedAction === null) {
      return; // 中间件阻止了更新
    }
    
    // 更新状态
    if (typeof key === 'object') {
      // 批量更新
      this.state = { ...this.state, ...key };
      if (!silent) {
        this.notifySubscribers('*', this.state);
      }
    } else {
      // 单个更新
      this.state[key] = processedAction.value;
      if (!silent) {
        this.notifySubscribers(key, processedAction.value, oldValue);
      }
    }
    
    // 更新最后活动时间
    this.state.app.lastActivity = Date.now();
  }

  /**
   * 获取状态
   * @param {string} key - 状态键，如果为空则返回整个状态
   * @returns {any} 状态值
   */
  getState(key = null) {
    if (key === null) {
      return { ...this.state };
    }
    
    // 支持点号路径，如 'ui.isLoading'
    if (key.includes('.')) {
      return this.getNestedValue(this.state, key);
    }
    
    return this.state[key];
  }

  /**
   * 获取嵌套值
   * @param {object} obj - 对象
   * @param {string} path - 路径
   * @returns {any} 值
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 设置嵌套值
   * @param {string} path - 路径
   * @param {any} value - 值
   */
  setNestedValue(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, this.state);
    
    const oldValue = target[lastKey];
    target[lastKey] = value;
    
    this.notifySubscribers(path, value, oldValue);
  }

  /**
   * 订阅状态变化
   * @param {string} key - 状态键
   * @param {function} callback - 回调函数
   * @returns {function} 取消订阅函数
   */
  subscribe(key, callback) {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    
    this.subscribers.get(key).add(callback);
    
    // 返回取消订阅函数
    return () => this.unsubscribe(key, callback);
  }

  /**
   * 取消订阅
   * @param {string} key - 状态键
   * @param {function} callback - 回调函数
   */
  unsubscribe(key, callback) {
    if (this.subscribers.has(key)) {
      this.subscribers.get(key).delete(callback);
      
      // 如果没有订阅者了，删除键
      if (this.subscribers.get(key).size === 0) {
        this.subscribers.delete(key);
      }
    }
  }

  /**
   * 通知订阅者
   * @param {string} key - 状态键
   * @param {any} value - 新值
   * @param {any} oldValue - 旧值
   */
  notifySubscribers(key, value, oldValue) {
    // 通知特定键的订阅者
    if (this.subscribers.has(key)) {
      this.subscribers.get(key).forEach(callback => {
        try {
          callback(value, oldValue, key);
        } catch (error) {
          console.error('Error in state subscriber:', error);
        }
      });
    }
    
    // 通知全局订阅者
    if (this.subscribers.has('*')) {
      this.subscribers.get('*').forEach(callback => {
        try {
          callback(this.state, key, value, oldValue);
        } catch (error) {
          console.error('Error in global state subscriber:', error);
        }
      });
    }
  }

  /**
   * 添加中间件
   * @param {function} middleware - 中间件函数
   */
  addMiddleware(middleware) {
    this.middleware.push(middleware);
  }

  /**
   * 运行中间件
   * @param {object} action - 动作对象
   * @returns {object|null} 处理后的动作或null（阻止更新）
   */
  runMiddleware(action) {
    let processedAction = action;
    
    for (const middleware of this.middleware) {
      try {
        processedAction = middleware(processedAction, this.state);
        if (processedAction === null) {
          return null; // 中间件阻止了更新
        }
      } catch (error) {
        console.error('Error in middleware:', error);
      }
    }
    
    return processedAction;
  }

  /**
   * 重置状态
   * @param {object} newState - 新状态（可选）
   */
  reset(newState = null) {
    const oldState = this.state;
    this.state = newState || this.getInitialState();
    this.notifySubscribers('*', this.state, oldState);
  }

  /**
   * 获取状态快照
   * @returns {object} 状态快照
   */
  getSnapshot() {
    return JSON.parse(JSON.stringify(this.state));
  }

  /**
   * 从快照恢复状态
   * @param {object} snapshot - 状态快照
   */
  restoreFromSnapshot(snapshot) {
    const oldState = this.state;
    this.state = { ...this.getInitialState(), ...snapshot };
    this.notifySubscribers('*', this.state, oldState);
  }

  /**
   * 分发动作
   * @param {object} action - 动作对象
   * @returns {object} 新状态
   */
  dispatch(action) {
    if (!action || typeof action !== 'object' || !action.type) {
      console.error('Invalid action:', action);
      return this.state;
    }

    try {
      // 运行中间件
      const processedAction = this.runMiddleware(action, this.state);
      if (processedAction === null) {
        return this.state; // 中间件阻止了更新
      }

      // 使用reducer处理状态更新
      const newState = StateReducer.reduce(this.state, processedAction);

      if (newState !== this.state) {
        const oldState = this.state;
        this.state = newState;

        // 通知订阅者
        this.notifySubscribers('*', newState, oldState);

        // 更新最后活动时间
        this.state.app.lastActivity = Date.now();
      }

      return this.state;
    } catch (error) {
      console.error('Error dispatching action:', error);
      return this.state;
    }
  }

  /**
   * 调试信息
   */
  debug() {
    console.group('Store Debug Info');
    console.log('Current State:', this.state);
    console.log('Subscribers:', this.subscribers);
    console.log('Middleware:', this.middleware);
    console.groupEnd();
  }
}

/**
 * 常用的中间件
 */

/**
 * 日志中间件
 */
export const loggerMiddleware = (action, state) => {
  console.log(`[Store] ${action.type}:`, {
    key: action.key,
    oldValue: action.oldValue,
    newValue: action.value
  });
  return action;
};

/**
 * 持久化中间件
 */
export const persistenceMiddleware = (action, state) => {
  // 只持久化设置
  if (action.key === 'settings') {
    try {
      localStorage.setItem('chat-settings', JSON.stringify(action.value));
    } catch (error) {
      console.error('Failed to persist settings:', error);
    }
  }
  return action;
};

/**
 * 验证中间件
 */
export const validationMiddleware = (action, state) => {
  if (action.key === 'settings') {
    // 验证设置格式
    const validThemes = ['auto', 'light', 'dark', 'high-contrast'];
    const validFontSizes = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
    
    if (action.value.theme && !validThemes.includes(action.value.theme)) {
      console.warn('Invalid theme value:', action.value.theme);
      action.value.theme = 'auto';
    }
    
    if (action.value.fontSize && !validFontSizes.includes(action.value.fontSize)) {
      console.warn('Invalid fontSize value:', action.value.fontSize);
      action.value.fontSize = 'md';
    }
  }
  
  return action;
};

// 导出默认实例
export default new Store();
