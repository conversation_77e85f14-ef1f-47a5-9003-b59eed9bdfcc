/* 组件样式库 */

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--button-padding-y) var(--button-padding-x);
  min-height: var(--button-min-height);
  border: 1px solid transparent;
  border-radius: var(--button-border-radius);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-border-focus);
}

.btn:hover:not(:disabled) {
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:active:not(:disabled) {
  background-color: var(--color-surface-active);
  border-color: var(--color-border-active);
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: var(--color-secondary-foreground);
  border-color: var(--color-border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-secondary-hover);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-foreground);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--color-surface-hover);
}

.btn-icon {
  padding: var(--spacing-sm);
  min-height: auto;
  width: 32px;
  height: 32px;
}

/* 输入框组件 */
.input {
  width: 100%;
  padding: var(--input-padding-y) var(--input-padding-x);
  border: 1px solid var(--color-border-input);
  border-radius: var(--input-border-radius);
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: border-color var(--transition-fast);
  resize: none;
  outline: none;
}

.input:focus {
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 1px var(--color-border-focus), 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
  transform: scale(1.01);
}

.input::placeholder {
  color: var(--color-foreground-muted);
}

/* 消息组件 */
.message {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--message-gap);
  width: 100%;
  word-wrap: break-word;
}

.message-user {
  align-items: flex-end;
  max-width: var(--message-user-max-width);
  margin-left: auto;
}

.message-ai {
  align-items: flex-start;
  max-width: var(--message-ai-max-width);
}

.message-content {
  position: relative;
  padding: var(--message-padding);
  border-radius: var(--message-border-radius);
  border: 1px solid var(--color-border);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  transition: all var(--transition-fast);
}

.message-user .message-content {
  background-color: var(--color-surface-active);
  color: var(--color-foreground-active);
}

.message-ai .message-content {
  background-color: var(--color-surface-hover);
  color: var(--color-foreground);
}

.message-content:hover {
  box-shadow: var(--shadow-sm);
}

.message-actions {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.message-content:hover .message-actions {
  opacity: 1;
}

.message-time {
  font-size: var(--font-size-xs);
  color: var(--color-foreground-muted);
  margin-top: var(--spacing-sm);
  opacity: 0.7;
}

/* 折叠组件 */
.message-text {
  transition: max-height var(--transition-normal) ease-out;
  overflow: hidden;
  white-space: pre-wrap; /* 保持换行符和空格 */
  word-wrap: break-word; /* 长单词换行 */
}

.message-collapsed {
  max-height: 5.5em; /* 约5行的高度 */
  position: relative;
}

.message-collapsed::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1.5em;
  background: linear-gradient(transparent, var(--color-surface-hover));
  pointer-events: none;
  opacity: 1;
  transition: opacity var(--transition-fast);
}

.message-expanded .message-collapsed::after {
  opacity: 0;
}

.message-expand-btn {
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.message-expand-btn:hover {
  background-color: var(--color-surface-hover);
  text-decoration: none;
}

.message-expand-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-border-focus);
}

.message-expand-btn::before {
  content: '▼';
  font-size: 10px;
  transition: transform var(--transition-fast);
}

.message-expanded .message-expand-btn::before {
  transform: rotate(180deg);
}

/* 折叠状态指示器 */
.message-collapse-indicator {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: var(--color-primary);
  color: var(--color-primary-foreground);
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  opacity: 0.8;
  pointer-events: none;
  z-index: 1;
}

/* 消息编辑功能 */
.message.editing {
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 1px var(--color-border-focus);
}

.message-editor-container {
  margin: var(--spacing-sm) 0;
}

.message-editor {
  width: 100%;
  min-height: 60px;
  max-height: 30vh;
  padding: var(--spacing-md);
  border: 1px solid var(--color-border-focus);
  border-radius: var(--radius-sm);
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  resize: none;
  outline: none;
  transition: border-color var(--transition-fast);
}

.message-editor:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 1px var(--color-primary);
}

.message-edit-controls {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  justify-content: flex-end;
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  min-height: 28px;
  font-size: var(--font-size-sm);
}

.message.editing .message-actions {
  opacity: 0;
  pointer-events: none;
}

.message.editing .message-time {
  opacity: 0.5;
}

/* 主题选择器组件 */
.theme-selector-component {
  padding: var(--spacing-lg);
}

.theme-selector-header {
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.theme-selector-title {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-foreground);
}

.theme-selector-description {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.theme-card {
  background: var(--color-surface);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  outline: none;
}

.theme-card:hover {
  border-color: var(--color-border-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-card:focus {
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 2px var(--color-border-focus);
}

.theme-card.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-background);
}

.theme-preview {
  width: 100%;
  height: 60px;
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.theme-icon {
  font-size: 24px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.theme-info {
  text-align: center;
  margin-bottom: var(--spacing-sm);
}

.theme-name {
  font-weight: var(--font-weight-bold);
  color: var(--color-foreground);
  margin-bottom: var(--spacing-xs);
}

.theme-description {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
}

.theme-check {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-primary);
  color: var(--color-primary-foreground);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.theme-card.selected .theme-check {
  opacity: 1;
}

.theme-selector-footer {
  text-align: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

.current-theme-info {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
}

.current-theme-name {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}

/* 字号调整组件 */
.font-size-adjuster-component {
  padding: var(--spacing-lg);
}

.font-size-adjuster-header {
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.font-size-adjuster-title {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-foreground);
}

.font-size-adjuster-description {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
}

.font-size-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.font-size-decrease,
.font-size-increase {
  min-width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  transition: all var(--transition-fast);
}

.font-size-decrease:disabled,
.font-size-increase:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.font-size-slider-container {
  flex: 1;
  position: relative;
  padding: var(--spacing-sm) 0;
}

.font-size-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--color-border);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.font-size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-primary);
  cursor: pointer;
  border: 2px solid var(--color-surface);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-fast);
}

.font-size-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.font-size-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-primary);
  cursor: pointer;
  border: 2px solid var(--color-surface);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-fast);
}

.font-size-marks {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  pointer-events: none;
}

.font-size-mark {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-border);
  transform: translate(-50%, -50%);
  transition: all var(--transition-fast);
}

.font-size-mark.active {
  background: var(--color-primary);
  transform: translate(-50%, -50%) scale(1.2);
}

.font-size-info {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.current-font-info {
  margin-bottom: var(--spacing-xs);
}

.current-font-name {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
  color: var(--color-foreground);
}

.current-font-size {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
  margin-left: var(--spacing-xs);
}

.current-font-description {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
}

.font-size-preview {
  background: var(--color-surface-hover);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.preview-label {
  font-size: var(--font-size-sm);
  color: var(--color-foreground-muted);
  margin-bottom: var(--spacing-sm);
}

.preview-text {
  color: var(--color-foreground);
  line-height: var(--line-height-normal);
  transition: font-size var(--transition-normal);
}

.font-size-actions {
  text-align: center;
}

/* 通知管理器 */
.notification-manager {
  position: fixed;
  z-index: 10000;
  pointer-events: none;
  max-width: 400px;
}

.notification {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  position: relative;
  pointer-events: auto;
  min-width: 30%;
  max-width: 75%;
  opacity: 0;
  transform: translateX(100%);
  transition: all var(--transition-normal) ease-out;
}

.notification.notification-enter {
  opacity: 1;
  transform: translateX(0) scale(1);
  animation: notificationSlideIn var(--duration-normal) var(--easing-bounce);
}

.notification.notification-exit {
  opacity: 0;
  transform: translateX(100%) scale(0.95);
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  max-height: 0;
  overflow: hidden;
  animation: notificationSlideOut var(--duration-fast) var(--easing-ease-in);
}

.notification.notification-updated {
  transform: scale(1.02);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  animation: notificationPulse var(--duration-fast) var(--easing-ease-out);
}

/* 通知动画关键帧 */
@keyframes notificationSlideIn {
  0% {
    opacity: 0;
    transform: translateX(100%) scale(0.9);
  }
  50% {
    opacity: 1;
    transform: translateX(-10px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes notificationSlideOut {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(100%) scale(0.95);
  }
}

@keyframes notificationPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* 通知类型样式 */
.notification-success {
  border-left: 4px solid var(--color-success);
}

.notification-warning {
  border-left: 4px solid var(--color-warning);
}

.notification-error {
  border-left: 4px solid var(--color-error);
}

.notification-info {
  border-left: 4px solid var(--color-primary);
}

.notification-loading {
  border-left: 4px solid var(--color-primary);
}

.notification-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--color-surface);
  flex-shrink: 0;
}

.notification-success .notification-icon {
  background: var(--color-success);
}

.notification-warning .notification-icon {
  background: var(--color-warning);
}

.notification-error .notification-icon {
  background: var(--color-error);
}

.notification-info .notification-icon {
  background: var(--color-primary);
}

.notification-loading .notification-icon {
  background: var(--color-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-message {
  color: var(--color-foreground);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacing-xs);
  word-wrap: break-word;
}

.notification-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.notification-action-btn {
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  min-height: 24px;
}

.notification-close-btn {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  color: var(--color-foreground-muted);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 1;
  transition: all var(--transition-fast);
}

.notification-close-btn:hover {
  background: var(--color-surface-hover);
  color: var(--color-foreground);
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  overflow: hidden;
}

.notification-progress-bar {
  height: 100%;
  width: 100%;
  background: var(--color-primary);
  transition: width linear;
}

.notification-success .notification-progress-bar {
  background: var(--color-success);
}

.notification-warning .notification-progress-bar {
  background: var(--color-warning);
}

.notification-error .notification-progress-bar {
  background: var(--color-error);
}

/* 位置样式 */
.notification-manager.position-top-left .notification {
  transform: translateX(-100%);
}

.notification-manager.position-top-left .notification.notification-enter {
  transform: translateX(0);
}

.notification-manager.position-top-left .notification.notification-exit {
  transform: translateX(-100%);
}

.notification-manager.position-bottom-right .notification,
.notification-manager.position-bottom-left .notification,
.notification-manager.position-bottom-center .notification {
  transform: translateY(100%);
}

.notification-manager.position-bottom-right .notification.notification-enter,
.notification-manager.position-bottom-left .notification.notification-enter,
.notification-manager.position-bottom-center .notification.notification-enter {
  transform: translateY(0);
}

.notification-manager.position-bottom-right .notification.notification-exit,
.notification-manager.position-bottom-left .notification.notification-exit,
.notification-manager.position-bottom-center .notification.notification-exit {
  transform: translateY(100%);
}

/* Toast通知组件 */
.toast-container {
  position: fixed;
  top: var(--toast-offset);
  right: var(--toast-offset);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-sm);
  pointer-events: none;
}

.toast {
  min-width: 200px;
  max-width: var(--toast-width);
  width: fit-content;
  padding: var(--toast-padding);
  border-radius: var(--toast-border-radius);
  border: 1px solid var(--color-border);
  background-color: var(--color-surface);
  color: var(--color-foreground);
  box-shadow: var(--shadow-lg);
  pointer-events: auto;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
}

.toast.show {
  transform: translateX(0);
}

.toast-success {
  border-left: 4px solid var(--color-success);
}

.toast-warning {
  border-left: 4px solid var(--color-warning);
}

.toast-error {
  border-left: 4px solid var(--color-error);
}

.toast-info {
  border-left: 4px solid var(--color-info);
}

.toast-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.toast-title {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.toast-close {
  background: none;
  border: none;
  color: var(--color-foreground-muted);
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-body {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/* 版本控制 */
.message-version-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-right: var(--spacing-sm);
}

.version-btn {
  width: 20px;
  height: 20px;
  padding: 0;
  font-size: 10px;
  line-height: 1;
  border-radius: var(--radius-sm);
  background: var(--color-surface-hover);
  border: 1px solid var(--color-border);
  color: var(--color-foreground-muted);
  transition: all var(--transition-fast);
}

.version-btn:hover:not(:disabled) {
  background: var(--color-primary);
  color: var(--color-surface);
  border-color: var(--color-primary);
}

.version-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.version-info {
  font-size: var(--font-size-xs);
  color: var(--color-foreground-muted);
  min-width: 30px;
  text-align: center;
}

/* 加载状态 */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-border);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 动画工具类 */
.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--easing-ease-out);
}

.animate-fade-out {
  animation: fadeOut var(--duration-normal) var(--easing-ease-in);
}

.animate-slide-in-left {
  animation: slideInLeft var(--duration-normal) var(--easing-ease-out);
}

.animate-slide-in-right {
  animation: slideInRight var(--duration-normal) var(--easing-ease-out);
}

.animate-slide-in-up {
  animation: slideInUp var(--duration-normal) var(--easing-ease-out);
}

.animate-slide-in-down {
  animation: slideInDown var(--duration-normal) var(--easing-ease-out);
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--easing-bounce);
}

.animate-bounce-in {
  animation: bounceIn var(--duration-slow) var(--easing-bounce);
}

.animate-shake {
  animation: shake var(--duration-slow) var(--easing-ease-in-out);
}

.animate-pulse {
  animation: pulse 2s var(--easing-ease-in-out) infinite;
}

/* 动画关键帧 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
  20%, 40%, 60%, 80% { transform: translateX(10px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 微交互效果 */
.hover-lift {
  transition: transform var(--transition-fast) var(--easing-ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform var(--transition-fast) var(--easing-ease-out);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow {
  transition: box-shadow var(--transition-fast) var(--easing-ease-out);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.3);
}

.focus-ring {
  transition: outline var(--transition-fast) var(--easing-ease-out);
}

.focus-ring:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 加载状态 */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-border);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-pulse {
    animation: none;
  }
}

/* 工具类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
