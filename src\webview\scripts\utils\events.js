/**
 * 事件管理系统
 * 提供发布订阅模式的事件管理功能
 */

/**
 * 事件管理器类
 */
export class EventManager {
  constructor() {
    this.events = new Map();
    this.onceEvents = new Map();
    this.maxListeners = 10;
    
    // 绑定方法上下文
    this.on = this.on.bind(this);
    this.off = this.off.bind(this);
    this.emit = this.emit.bind(this);
    this.once = this.once.bind(this);
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名
   * @param {function} listener - 监听器函数
   * @param {object} options - 选项
   * @returns {function} 取消监听函数
   */
  on(event, listener, options = {}) {
    if (typeof listener !== 'function') {
      throw new TypeError('Listener must be a function');
    }

    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listeners = this.events.get(event);
    
    // 检查监听器数量限制
    if (listeners.length >= this.maxListeners) {
      console.warn(`Maximum listeners (${this.maxListeners}) exceeded for event: ${event}`);
    }

    // 添加监听器
    const listenerInfo = {
      listener,
      options,
      id: this.generateId()
    };
    
    if (options.prepend) {
      listeners.unshift(listenerInfo);
    } else {
      listeners.push(listenerInfo);
    }

    // 返回取消监听函数
    return () => this.off(event, listener);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名
   * @param {function} listener - 监听器函数
   * @returns {boolean} 是否成功移除
   */
  off(event, listener) {
    if (!this.events.has(event)) {
      return false;
    }

    const listeners = this.events.get(event);
    const index = listeners.findIndex(item => item.listener === listener);
    
    if (index !== -1) {
      listeners.splice(index, 1);
      
      // 如果没有监听器了，删除事件
      if (listeners.length === 0) {
        this.events.delete(event);
      }
      
      return true;
    }

    return false;
  }

  /**
   * 添加一次性事件监听器
   * @param {string} event - 事件名
   * @param {function} listener - 监听器函数
   * @param {object} options - 选项
   * @returns {Promise} Promise对象
   */
  once(event, listener, options = {}) {
    return new Promise((resolve) => {
      const onceListener = (...args) => {
        this.off(event, onceListener);
        if (listener) {
          const result = listener(...args);
          resolve(result);
        } else {
          resolve(args[0]);
        }
      };
      
      this.on(event, onceListener, options);
    });
  }

  /**
   * 触发事件
   * @param {string} event - 事件名
   * @param {...any} args - 参数
   * @returns {boolean} 是否有监听器处理了事件
   */
  emit(event, ...args) {
    let hasListeners = false;

    // 触发具体事件的监听器
    if (this.events.has(event)) {
      const listeners = [...this.events.get(event)]; // 复制数组避免在遍历时修改
      hasListeners = true;

      listeners.forEach(({ listener, options }) => {
        try {
          // 异步执行监听器
          if (options.async) {
            setTimeout(() => listener(...args), 0);
          } else {
            listener(...args);
          }
        } catch (error) {
          console.error(`Error in event listener for "${event}":`, error);
          this.emit('error', error, event, listener);
        }
      });
    }

    // 触发通配符监听器
    if (this.events.has('*')) {
      const wildcardListeners = [...this.events.get('*')];
      hasListeners = true;

      wildcardListeners.forEach(({ listener, options }) => {
        try {
          if (options.async) {
            setTimeout(() => listener(event, ...args), 0);
          } else {
            listener(event, ...args);
          }
        } catch (error) {
          console.error(`Error in wildcard listener for "${event}":`, error);
        }
      });
    }

    return hasListeners;
  }

  /**
   * 移除所有监听器
   * @param {string} event - 事件名，如果不提供则移除所有事件的监听器
   */
  removeAllListeners(event) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }

  /**
   * 获取事件的监听器数量
   * @param {string} event - 事件名
   * @returns {number} 监听器数量
   */
  listenerCount(event) {
    return this.events.has(event) ? this.events.get(event).length : 0;
  }

  /**
   * 获取所有事件名
   * @returns {Array} 事件名数组
   */
  eventNames() {
    return Array.from(this.events.keys());
  }

  /**
   * 设置最大监听器数量
   * @param {number} max - 最大数量
   */
  setMaxListeners(max) {
    this.maxListeners = max;
  }

  /**
   * 获取最大监听器数量
   * @returns {number} 最大数量
   */
  getMaxListeners() {
    return this.maxListeners;
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * 调试信息
   */
  debug() {
    console.group('EventManager Debug Info');
    console.log('Events:', this.events);
    console.log('Event Names:', this.eventNames());
    console.log('Total Events:', this.events.size);
    
    this.events.forEach((listeners, event) => {
      console.log(`Event "${event}": ${listeners.length} listeners`);
    });
    
    console.groupEnd();
  }
}

/**
 * DOM事件工具类
 */
export class DOMEventUtils {
  /**
   * 添加DOM事件监听器
   * @param {Element} element - 元素
   * @param {string} event - 事件名
   * @param {function} handler - 处理函数
   * @param {object} options - 选项
   * @returns {function} 移除监听器函数
   */
  static on(element, event, handler, options = {}) {
    element.addEventListener(event, handler, options);
    return () => element.removeEventListener(event, handler, options);
  }

  /**
   * 移除DOM事件监听器
   * @param {Element} element - 元素
   * @param {string} event - 事件名
   * @param {function} handler - 处理函数
   * @param {object} options - 选项
   */
  static off(element, event, handler, options = {}) {
    element.removeEventListener(event, handler, options);
  }

  /**
   * 添加一次性DOM事件监听器
   * @param {Element} element - 元素
   * @param {string} event - 事件名
   * @param {function} handler - 处理函数
   * @param {object} options - 选项
   * @returns {Promise} Promise对象
   */
  static once(element, event, handler, options = {}) {
    return new Promise((resolve) => {
      const onceHandler = (e) => {
        element.removeEventListener(event, onceHandler, options);
        if (handler) {
          const result = handler(e);
          resolve(result);
        } else {
          resolve(e);
        }
      };
      
      element.addEventListener(event, onceHandler, options);
    });
  }

  /**
   * 事件委托
   * @param {Element} container - 容器元素
   * @param {string} selector - 选择器
   * @param {string} event - 事件名
   * @param {function} handler - 处理函数
   * @returns {function} 移除监听器函数
   */
  static delegate(container, selector, event, handler) {
    const delegateHandler = (e) => {
      const target = e.target.closest(selector);
      if (target && container.contains(target)) {
        handler.call(target, e);
      }
    };
    
    container.addEventListener(event, delegateHandler);
    return () => container.removeEventListener(event, delegateHandler);
  }

  /**
   * 触发自定义事件
   * @param {Element} element - 元素
   * @param {string} eventName - 事件名
   * @param {any} detail - 事件详情
   * @param {object} options - 选项
   */
  static trigger(element, eventName, detail = null, options = {}) {
    const event = new CustomEvent(eventName, {
      detail,
      bubbles: true,
      cancelable: true,
      ...options
    });
    
    element.dispatchEvent(event);
  }
}

// 创建全局事件管理器实例
export const globalEventManager = new EventManager();

// 导出默认实例
export default globalEventManager;
