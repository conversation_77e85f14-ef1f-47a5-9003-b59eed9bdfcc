/**
 * 消息组件
 * 负责消息的显示、编辑、折叠等功能
 */

import BaseComponent from './base.js';
import { DOMUtils } from '../utils/dom.js';
import { StorageUtils } from '../utils/storage.js';
import MessageManager from './message-manager.js';
import { globalToolbarNotifications, ToolbarActions } from './toolbar-notifications.js';
import { globalAnimationManager, AnimationTypes, MicroInteractions } from '../utils/animations.js';

/**
 * 消息组件类
 */
export class MessageComponent extends BaseComponent {
  constructor(store, eventManager) {
    const messagesContainer = document.getElementById('messages');
    super(messagesContainer, {
      className: 'message-component',
      autoInit: true
    });
    
    this.store = store;
    this.globalEventManager = eventManager;
    this.messages = [];
    this.autoScroll = true;

    // 初始化消息管理器
    this.messageManager = new MessageManager(store, eventManager);
    
    // 绑定方法上下文
    this.addMessage = this.addMessage.bind(this);
    this.editMessage = this.handleEditMessage.bind(this);
    this.deleteMessage = this.handleDeleteMessage.bind(this);
    this.clearMessages = this.clearMessages.bind(this);
    this.exportMessages = this.exportMessages.bind(this);
  }

  /**
   * 获取初始状态
   */
  getInitialState() {
    return {
      ...super.getInitialState(),
      messageCount: 0,
      isTyping: false
    };
  }

  /**
   * 初始化后钩子
   */
  async afterInit() {
    // 监听全局事件
    this.globalEventManager.on('message:add', this.addMessage);
    this.globalEventManager.on('message:edit', this.editMessage);
    this.globalEventManager.on('message:delete', this.deleteMessage);
    this.globalEventManager.on('message:clear', this.clearMessages);
    
    // 监听状态变化
    this.store.subscribe('messages', (messages) => {
      this.messages = messages;
      this.setState('messageCount', messages.length);
    });
    
    this.store.subscribe('autoScroll', (autoScroll) => {
      this.autoScroll = autoScroll;
    });
    
    // 设置消息容器
    this.setupMessageContainer();

    // 加载历史消息
    this.loadMessages();
  }

  /**
   * 设置消息容器
   */
  setupMessageContainer() {
    if (!this.element) {return;}

    // 如果容器有messages-wrapper子元素，使用它作为实际的消息容器
    const wrapper = this.element.querySelector('.messages-wrapper');
    if (wrapper) {
      this.messagesContainer = wrapper;
      wrapper.className = 'messages-list';
    } else {
      this.element.className = 'messages-list';
      this.messagesContainer = this.element;
    }
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (!this.element) {return;}
    
    // 使用事件委托处理消息操作
    this.element.addEventListener('click', (e) => {
      const target = e.target;
      
      // 编辑按钮
      if (target.classList.contains('message-edit-btn')) {
        const messageElement = target.closest('.message');
        const messageId = messageElement.dataset.messageId;
        this.handleEditMessage(messageId);
      }
      
      // 删除按钮
      if (target.classList.contains('message-delete-btn')) {
        const messageElement = target.closest('.message');
        const messageId = messageElement.dataset.messageId;
        this.handleDeleteMessage(messageId);
      }
      
      // 展开/收起按钮
      if (target.classList.contains('message-expand-btn')) {
        const messageElement = target.closest('.message');
        this.toggleMessageExpansion(messageElement);
      }
      
      // 复制按钮
      if (target.classList.contains('message-copy-btn')) {
        const messageElement = target.closest('.message');
        this.copyMessage(messageElement);
      }
    });

    // 为交互元素添加微交互效果
    this.setupMicroInteractions();
  }

  /**
   * 设置微交互效果
   */
  setupMicroInteractions() {
    // 为所有按钮添加悬停和波纹效果
    const buttons = this.element.querySelectorAll('.btn, .message-action-btn, .message-expand-btn');
    buttons.forEach(button => {
      MicroInteractions.addHoverEffect(button, { scale: 1.05 });
      MicroInteractions.addRippleEffect(button);
      MicroInteractions.addFocusRing(button);
    });

    // 为消息内容添加悬停效果
    const messageContents = this.element.querySelectorAll('.message-content');
    messageContents.forEach(content => {
      MicroInteractions.addHoverEffect(content, { scale: 1.01 });
    });
  }

  /**
   * 执行渲染（只渲染用户消息）
   */
  async doRender() {
    if (!this.messagesContainer) {return;}

    // 清空容器
    this.messagesContainer.innerHTML = '';

    // 过滤并渲染用户消息（AI回复内嵌在版本中）
    const userMessages = this.messages.filter(message => message.isUser);

    userMessages.forEach(message => {
      const messageElement = this.createMessageElement(message);
      if (messageElement) {
        this.messagesContainer.appendChild(messageElement);
      }
    });

    // 如果发现非用户消息，记录警告
    const nonUserMessages = this.messages.filter(message => !message.isUser);
    if (nonUserMessages.length > 0) {
      console.warn(`检测到 ${nonUserMessages.length} 个独立AI消息，这些消息将被忽略:`,
        nonUserMessages.map(m => m.id));
    }

    // 设置微交互效果
    this.setupMicroInteractions();

    // 自动滚动到底部
    if (this.autoScroll) {
      this.scrollToBottom();
    }
  }

  /**
   * 加载消息
   */
  loadMessages() {
    const messages = this.store.getState('messages') || [];
    this.messages = messages;
    this.render();
  }

  /**
   * 添加消息（只添加用户消息）
   * @param {object} messageData - 消息数据
   * @returns {object} 添加的消息对象
   */
  addMessage(messageData) {
    // 拒绝添加AI消息
    if (messageData.isUser === false) {
      console.warn('不允许添加独立的AI消息，AI回复应该内嵌在用户消息版本中');
      return null;
    }

    // 使用新的数据结构创建函数
    const message = this.createMessageFromData(messageData);

    this.messages.push(message);
    this.store.setState('messages', this.messages);

    // 添加到存储
    StorageUtils.addMessageToHistory(message);

    // 创建并添加消息元素
    const messageElement = this.createMessageElement(message);
    if (messageElement) {
      this.messagesContainer.appendChild(messageElement);

      // 添加进入动画
      this.animateMessageEntry(messageElement, true); // 总是用户消息

      // 检查是否需要折叠
      this.checkMessageCollapse(messageElement);
    }

    // 自动滚动
    if (this.autoScroll) {
      this.scrollToBottom();
    }

    this.emit('messageAdded', message);

    return message;
  }

  /**
   * 从数据创建消息对象
   * @param {object} messageData - 消息数据
   * @returns {object} 消息对象
   */
  createMessageFromData(messageData) {
    // 如果有现成的版本数据，使用它
    if (messageData.versions && Array.isArray(messageData.versions)) {
      return {
        id: messageData.id || this.generateMessageId(),
        isUser: true, // 只有用户消息
        timestamp: messageData.timestamp || Date.now(),
        edited: messageData.edited || false,
        collapsed: messageData.collapsed || false,
        type: messageData.type || 'text',
        versions: messageData.versions,
        currentVersionIndex: messageData.currentVersionIndex || 0,
        metadata: messageData.metadata || {}
      };
    }

    // 否则创建新的消息结构
    const messageId = messageData.id || this.generateMessageId();
    const timestamp = messageData.timestamp || Date.now();
    const content = messageData.content || '';

    return {
      id: messageId,
      isUser: true,
      timestamp: timestamp,
      edited: false,
      collapsed: false,
      type: messageData.type || 'text',
      versions: [{
        id: `ver_${timestamp}_${Math.random().toString(36).substring(2, 11)}`,
        content: content,
        timestamp: timestamp,
        aiReply: null,
        metadata: {}
      }],
      currentVersionIndex: 0,
      metadata: messageData.metadata || {}
    };
  }

  /**
   * 创建消息元素（支持内嵌AI回复）
   * @param {object} message - 消息对象
   * @returns {Element} 消息元素
   */
  createMessageElement(message) {
    // 只处理用户消息，AI回复内嵌在版本中
    if (!message.isUser) {
      console.warn('不应该渲染独立的AI消息:', message.id);
      return null;
    }

    const messageDiv = DOMUtils.createElement('div', {
      className: 'message message-user',
      'data-message-id': message.id
    });

    // 获取当前版本
    const currentVersion = this.getCurrentVersion(message);
    if (!currentVersion) {
      console.error('消息没有有效的当前版本:', message.id);
      return null;
    }

    // 创建用户消息内容
    const userMessageContent = this.createUserMessageContent(message, currentVersion);
    messageDiv.appendChild(userMessageContent);

    // 创建AI回复内容（如果存在）
    if (currentVersion.aiReply) {
      const aiReplyContent = this.createAIReplyContent(currentVersion.aiReply, message);
      messageDiv.appendChild(aiReplyContent);
    }

    // 添加消息时间戳
    const messageTime = this.createMessageTimeElement(message);
    messageDiv.appendChild(messageTime);

    return messageDiv;
  }

  /**
   * 获取消息的当前版本
   * @param {object} message - 消息对象
   * @returns {object|null} 当前版本对象
   */
  getCurrentVersion(message) {
    if (!message.versions || message.versions.length === 0) {
      return null;
    }

    const currentIndex = message.currentVersionIndex || 0;
    if (currentIndex < 0 || currentIndex >= message.versions.length) {
      return message.versions[0]; // 回退到第一个版本
    }

    return message.versions[currentIndex];
  }

  /**
   * 创建用户消息内容
   * @param {object} message - 消息对象
   * @param {object} version - 版本对象
   * @returns {Element} 用户消息内容元素
   */
  createUserMessageContent(message, version) {
    const userContent = DOMUtils.createElement('div', {
      className: 'user-message-content'
    });

    // 用户消息文本
    const messageText = DOMUtils.createElement('div', {
      className: 'message-text user-message-text',
      textContent: version.content
    });

    // 消息操作按钮
    const messageActions = this.createMessageActions(message);

    userContent.appendChild(messageText);
    userContent.appendChild(messageActions);

    return userContent;
  }

  /**
   * 创建AI回复内容
   * @param {object} aiReply - AI回复对象
   * @param {object} message - 父消息对象
   * @returns {Element} AI回复内容元素
   */
  createAIReplyContent(aiReply, message) {
    const aiContent = DOMUtils.createElement('div', {
      className: 'ai-reply-content',
      'data-ai-reply-id': aiReply.id
    });

    // AI回复状态指示器
    const statusIndicator = this.createAIReplyStatusIndicator(aiReply);
    aiContent.appendChild(statusIndicator);

    // AI回复文本
    const aiText = DOMUtils.createElement('div', {
      className: 'message-text ai-reply-text',
      textContent: aiReply.content
    });
    aiContent.appendChild(aiText);

    // AI回复操作按钮
    const aiActions = this.createAIReplyActions(aiReply, message);
    aiContent.appendChild(aiActions);

    // AI回复元数据（如果需要显示）
    if (aiReply.metadata && (aiReply.metadata.model || aiReply.metadata.tokens)) {
      const metadata = this.createAIReplyMetadata(aiReply);
      aiContent.appendChild(metadata);
    }

    return aiContent;
  }

  /**
   * 获取当前版本的消息内容（保持向后兼容）
   * @param {object} message - 消息对象
   * @returns {string} 当前版本的内容
   */
  getCurrentVersionContent(message) {
    const version = this.getCurrentVersion(message);
    return version ? version.content : (message.content || '');
  }

  /**
   * 创建消息操作按钮
   * @param {object} message - 消息对象
   * @returns {Element} 操作按钮容器
   */
  createMessageActions(message) {
    const messageActions = DOMUtils.createElement('div', {
      className: 'message-actions'
    });

    // 版本切换控件（如果有多个版本）
    if (message.versions && message.versions.length > 1) {
      const versionControls = this.createVersionControls(message);
      messageActions.appendChild(versionControls);
    }

    // 编辑按钮
    const editBtn = DOMUtils.createElement('button', {
      className: 'btn btn-icon message-edit-btn',
      title: '编辑消息',
      innerHTML: '✏️'
    });
    messageActions.appendChild(editBtn);

    // 复制按钮
    const copyBtn = DOMUtils.createElement('button', {
      className: 'btn btn-icon message-copy-btn',
      title: '复制消息',
      innerHTML: '📋'
    });
    messageActions.appendChild(copyBtn);

    // 删除按钮
    const deleteBtn = DOMUtils.createElement('button', {
      className: 'btn btn-icon message-delete-btn',
      title: '删除消息',
      innerHTML: '🗑️'
    });
    messageActions.appendChild(deleteBtn);

    return messageActions;
  }

  /**
   * 创建AI回复状态指示器
   * @param {object} aiReply - AI回复对象
   * @returns {Element} 状态指示器元素
   */
  createAIReplyStatusIndicator(aiReply) {
    const statusIndicator = DOMUtils.createElement('div', {
      className: `ai-reply-status ai-reply-status-${aiReply.status}`
    });

    let statusContent = '';
    let statusClass = '';

    switch (aiReply.status) {
      case 'pending':
        statusContent = '⏳ 等待回复...';
        statusClass = 'status-pending';
        break;
      case 'generating':
        statusContent = `🤖 正在生成... ${aiReply.progress || 0}%`;
        statusClass = 'status-generating';
        break;
      case 'completed':
        statusContent = '✅ AI回复';
        statusClass = 'status-completed';
        break;
      case 'error':
        statusContent = '❌ 生成失败';
        statusClass = 'status-error';
        break;
      case 'cancelled':
        statusContent = '⏹️ 已取消';
        statusClass = 'status-cancelled';
        break;
      case 'timeout':
        statusContent = '⏰ 超时';
        statusClass = 'status-timeout';
        break;
      default:
        statusContent = '🤖 AI回复';
        statusClass = 'status-unknown';
    }

    statusIndicator.className += ` ${statusClass}`;
    statusIndicator.textContent = statusContent;

    return statusIndicator;
  }

  /**
   * 创建AI回复操作按钮
   * @param {object} aiReply - AI回复对象
   * @param {object} message - 父消息对象
   * @returns {Element} AI回复操作按钮容器
   */
  createAIReplyActions(aiReply, message) {
    const aiActions = DOMUtils.createElement('div', {
      className: 'ai-reply-actions'
    });

    // 重新生成按钮
    if (aiReply.status === 'completed' || aiReply.status === 'error') {
      const regenerateBtn = DOMUtils.createElement('button', {
        className: 'btn btn-icon ai-reply-regenerate-btn',
        title: '重新生成回复',
        innerHTML: '🔄'
      });
      aiActions.appendChild(regenerateBtn);
    }

    // 取消按钮（仅在生成中显示）
    if (aiReply.status === 'generating' || aiReply.status === 'pending') {
      const cancelBtn = DOMUtils.createElement('button', {
        className: 'btn btn-icon ai-reply-cancel-btn',
        title: '取消生成',
        innerHTML: '⏹️'
      });
      aiActions.appendChild(cancelBtn);
    }

    // 复制AI回复按钮
    if (aiReply.status === 'completed' && aiReply.content) {
      const copyBtn = DOMUtils.createElement('button', {
        className: 'btn btn-icon ai-reply-copy-btn',
        title: '复制AI回复',
        innerHTML: '📋'
      });
      aiActions.appendChild(copyBtn);
    }

    return aiActions;
  }

  /**
   * 创建AI回复元数据显示
   * @param {object} aiReply - AI回复对象
   * @returns {Element} 元数据显示元素
   */
  createAIReplyMetadata(aiReply) {
    const metadata = DOMUtils.createElement('div', {
      className: 'ai-reply-metadata'
    });

    const metadataItems = [];

    if (aiReply.metadata.model) {
      metadataItems.push(`模型: ${aiReply.metadata.model}`);
    }

    if (aiReply.metadata.tokens) {
      metadataItems.push(`Token: ${aiReply.metadata.tokens}`);
    }

    if (aiReply.metadata.duration) {
      const duration = (aiReply.metadata.duration / 1000).toFixed(1);
      metadataItems.push(`耗时: ${duration}s`);
    }

    if (metadataItems.length > 0) {
      metadata.textContent = metadataItems.join(' • ');
    }

    return metadata;
  }

  /**
   * 创建消息时间戳元素
   * @param {object} message - 消息对象
   * @returns {Element} 时间戳元素
   */
  createMessageTimeElement(message) {
    const messageTime = DOMUtils.createElement('div', {
      className: 'message-time'
    });

    let timeText = this.formatTime(message.timestamp);
    if (message.edited && message.editedAt) {
      timeText += ` (已编辑 ${this.formatTime(message.editedAt)})`;
    }
    messageTime.textContent = timeText;

    return messageTime;
  }

  /**
   * 更新AI回复状态显示
   * @param {string} messageId - 消息ID
   * @param {object} aiReply - AI回复对象
   */
  updateAIReplyStatus(messageId, aiReply) {
    const messageElement = this.messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageElement) {
      console.warn('未找到消息元素:', messageId);
      return;
    }

    const aiReplyElement = messageElement.querySelector('.ai-reply-content');
    if (!aiReplyElement) {
      // 如果没有AI回复元素，创建一个新的
      const message = this.messages.find(m => m.id === messageId);
      if (message) {
        const newAIReplyElement = this.createAIReplyContent(aiReply, message);
        messageElement.appendChild(newAIReplyElement);
      }
      return;
    }

    // 更新状态指示器
    const statusIndicator = aiReplyElement.querySelector('.ai-reply-status');
    if (statusIndicator) {
      const newStatusIndicator = this.createAIReplyStatusIndicator(aiReply);
      statusIndicator.replaceWith(newStatusIndicator);
    }

    // 更新AI回复文本
    const aiText = aiReplyElement.querySelector('.ai-reply-text');
    if (aiText) {
      aiText.textContent = aiReply.content;
    }

    // 更新操作按钮
    const aiActions = aiReplyElement.querySelector('.ai-reply-actions');
    if (aiActions) {
      const message = this.messages.find(m => m.id === messageId);
      const newAIActions = this.createAIReplyActions(aiReply, message);
      aiActions.replaceWith(newAIActions);
    }

    // 更新元数据
    const existingMetadata = aiReplyElement.querySelector('.ai-reply-metadata');
    if (aiReply.metadata && (aiReply.metadata.model || aiReply.metadata.tokens)) {
      const newMetadata = this.createAIReplyMetadata(aiReply);
      if (existingMetadata) {
        existingMetadata.replaceWith(newMetadata);
      } else {
        aiReplyElement.appendChild(newMetadata);
      }
    } else if (existingMetadata) {
      existingMetadata.remove();
    }
  }

  /**
   * 移除AI回复显示
   * @param {string} messageId - 消息ID
   */
  removeAIReply(messageId) {
    const messageElement = this.messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageElement) {
      return;
    }

    const aiReplyElement = messageElement.querySelector('.ai-reply-content');
    if (aiReplyElement) {
      aiReplyElement.remove();
    }
  }

  /**
   * 刷新消息显示（重新渲染指定消息）
   * @param {string} messageId - 消息ID
   */
  refreshMessage(messageId) {
    const message = this.messages.find(m => m.id === messageId);
    if (!message) {
      console.warn('未找到消息:', messageId);
      return;
    }

    const messageElement = this.messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageElement) {
      console.warn('未找到消息元素:', messageId);
      return;
    }

    // 创建新的消息元素
    const newMessageElement = this.createMessageElement(message);
    if (newMessageElement) {
      messageElement.replaceWith(newMessageElement);
    }
  }

  /**
   * 检查消息是否需要折叠
   * @param {Element} messageElement - 消息元素
   */
  checkMessageCollapse(messageElement) {
    const messageText = messageElement.querySelector('.message-text');
    if (!messageText) {return;}

    // 使用更准确的行数检测方法
    const lineCount = this.calculateLineCount(messageText);

    // 如果超过5行，添加折叠功能
    if (lineCount > 5) {
      this.addCollapseFeature(messageElement, lineCount);
    }
  }

  /**
   * 计算文本行数
   * @param {Element} textElement - 文本元素
   * @returns {number} 行数
   */
  calculateLineCount(textElement) {
    // 保存原始样式
    const originalStyle = {
      height: textElement.style.height,
      maxHeight: textElement.style.maxHeight,
      overflow: textElement.style.overflow
    };

    // 临时设置样式以获取真实高度
    textElement.style.height = 'auto';
    textElement.style.maxHeight = 'none';
    textElement.style.overflow = 'visible';

    // 获取计算后的样式
    const computedStyle = getComputedStyle(textElement);
    const lineHeight = parseFloat(computedStyle.lineHeight);
    const paddingTop = parseFloat(computedStyle.paddingTop);
    const paddingBottom = parseFloat(computedStyle.paddingBottom);

    // 计算内容高度
    const contentHeight = textElement.scrollHeight - paddingTop - paddingBottom;

    // 恢复原始样式
    textElement.style.height = originalStyle.height;
    textElement.style.maxHeight = originalStyle.maxHeight;
    textElement.style.overflow = originalStyle.overflow;

    // 计算行数
    const lineCount = Math.ceil(contentHeight / lineHeight);

    return lineCount;
  }

  /**
   * 添加折叠功能
   * @param {Element} messageElement - 消息元素
   * @param {number} lineCount - 行数
   */
  addCollapseFeature(messageElement, lineCount) {
    const messageText = messageElement.querySelector('.message-text');
    const messageContent = messageElement.querySelector('.message-content');

    // 添加折叠样式
    DOMUtils.addClass(messageText, 'message-collapsed');

    // 创建展开按钮
    const expandBtn = DOMUtils.createElement('button', {
      className: 'message-expand-btn',
      textContent: '展开',
      title: `显示完整内容 (${lineCount} 行)`
    });

    // 创建折叠指示器
    const indicator = DOMUtils.createElement('div', {
      className: 'message-collapse-indicator',
      textContent: `${lineCount}行`
    });

    // 添加到消息中
    messageContent.appendChild(indicator);
    messageElement.insertBefore(expandBtn, messageElement.querySelector('.message-time'));

    // 标记消息为可折叠
    messageElement.dataset.collapsible = 'true';
    messageElement.dataset.lineCount = lineCount;
  }

  /**
   * 切换消息展开/收起
   * @param {Element} messageElement - 消息元素
   */
  toggleMessageExpansion(messageElement) {
    const messageText = messageElement.querySelector('.message-text');
    const expandBtn = messageElement.querySelector('.message-expand-btn');
    const indicator = messageElement.querySelector('.message-collapse-indicator');
    const lineCount = messageElement.dataset.lineCount || '多';

    const isCollapsed = DOMUtils.hasClass(messageText, 'message-collapsed');

    if (isCollapsed) {
      // 展开消息
      this.expandMessage(messageElement, messageText, expandBtn, indicator);
    } else {
      // 收起消息
      this.collapseMessage(messageElement, messageText, expandBtn, indicator, lineCount);
    }

    // 触发事件
    this.emit('messageToggled', {
      messageId: messageElement.dataset.messageId,
      expanded: !isCollapsed
    });
  }

  /**
   * 展开消息
   * @param {Element} messageElement - 消息元素
   * @param {Element} messageText - 消息文本元素
   * @param {Element} expandBtn - 展开按钮
   * @param {Element} indicator - 指示器
   */
  expandMessage(messageElement, messageText, expandBtn, indicator) {
    // 获取完整高度
    const fullHeight = this.getFullHeight(messageText);

    // 设置当前高度为起始点
    messageText.style.maxHeight = '5.5em';

    // 添加展开状态类
    DOMUtils.addClass(messageElement, 'message-expanded');
    DOMUtils.removeClass(messageText, 'message-collapsed');

    // 动画到完整高度
    requestAnimationFrame(() => {
      messageText.style.maxHeight = fullHeight + 'px';
    });

    // 动画完成后移除内联样式
    setTimeout(() => {
      messageText.style.maxHeight = '';
    }, 300);

    // 更新按钮和指示器
    expandBtn.textContent = '收起';
    expandBtn.title = '收起内容';
    if (indicator) {
      indicator.style.opacity = '0';
    }
  }

  /**
   * 收起消息
   * @param {Element} messageElement - 消息元素
   * @param {Element} messageText - 消息文本元素
   * @param {Element} expandBtn - 展开按钮
   * @param {Element} indicator - 指示器
   * @param {string} lineCount - 行数
   */
  collapseMessage(messageElement, messageText, expandBtn, indicator, lineCount) {
    // 获取当前高度
    const currentHeight = messageText.scrollHeight;

    // 设置当前高度为起始点
    messageText.style.maxHeight = currentHeight + 'px';

    // 移除展开状态类
    DOMUtils.removeClass(messageElement, 'message-expanded');

    // 动画到折叠高度
    requestAnimationFrame(() => {
      messageText.style.maxHeight = '5.5em';
      DOMUtils.addClass(messageText, 'message-collapsed');
    });

    // 动画完成后移除内联样式
    setTimeout(() => {
      messageText.style.maxHeight = '';
    }, 300);

    // 更新按钮和指示器
    expandBtn.textContent = '展开';
    expandBtn.title = `显示完整内容 (${lineCount} 行)`;
    if (indicator) {
      indicator.style.opacity = '1';
    }
  }

  /**
   * 获取元素的完整高度
   * @param {Element} element - 元素
   * @returns {number} 完整高度
   */
  getFullHeight(element) {
    // 保存当前样式
    const originalMaxHeight = element.style.maxHeight;
    const originalOverflow = element.style.overflow;

    // 临时移除限制
    element.style.maxHeight = 'none';
    element.style.overflow = 'visible';

    // 获取完整高度
    const fullHeight = element.scrollHeight;

    // 恢复样式
    element.style.maxHeight = originalMaxHeight;
    element.style.overflow = originalOverflow;

    return fullHeight;
  }

  /**
   * 处理编辑消息
   * @param {string} messageId - 消息ID
   */
  handleEditMessage(messageId) {
    const message = this.messages.find(m => m.id === messageId);
    if (!message) {return;}

    const messageElement = this.messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageElement) {return;}

    // 检查是否已经在编辑模式
    if (messageElement.classList.contains('editing')) {
      return;
    }

    // 进入编辑模式
    this.enterEditMode(messageElement, message);

    // 显示编辑模式提示
    // globalToolbarNotifications.notifyInfo(ToolbarActions.EDIT, '进入编辑模式，Ctrl+Enter保存，Esc取消');

    this.emit('messageEdit', message);
  }

  /**
   * 进入编辑模式
   * @param {Element} messageElement - 消息元素
   * @param {object} message - 消息对象
   */
  enterEditMode(messageElement, message) {
    const messageText = messageElement.querySelector('.message-text');
    if (!messageText) {return;}

    // 标记为编辑状态
    messageElement.classList.add('editing');

    // 保存原始内容 - 使用当前版本的内容
    const originalContent = this.getCurrentVersionContent(message);
    messageElement.dataset.originalContent = originalContent;

    // 创建编辑器
    const editor = this.createInlineEditor(originalContent);

    // 替换文本内容
    messageText.style.display = 'none';
    messageText.parentNode.insertBefore(editor, messageText);

    // 创建编辑控制按钮
    const editControls = this.createEditControls(messageElement);
    messageText.parentNode.insertBefore(editControls, messageText);

    // 聚焦编辑器
    const textarea = editor.querySelector('.message-editor');
    textarea.focus();
    textarea.setSelectionRange(textarea.value.length, textarea.value.length);

    // 调整编辑器高度
    this.adjustEditorHeight(textarea);
  }

  /**
   * 创建内联编辑器
   * @param {string} content - 初始内容
   * @returns {Element} 编辑器元素
   */
  createInlineEditor(content) {
    const editorContainer = DOMUtils.createElement('div', {
      className: 'message-editor-container'
    });

    const textarea = DOMUtils.createElement('textarea', {
      className: 'message-editor input',
      value: content,
      placeholder: '编辑消息内容...'
    });

    // 绑定键盘事件
    textarea.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        this.saveEdit(textarea.closest('.message'));
      } else if (e.key === 'Escape') {
        e.preventDefault();
        this.cancelEdit(textarea.closest('.message'));
      }
    });

    // 绑定输入事件
    textarea.addEventListener('input', () => {
      this.adjustEditorHeight(textarea);
    });

    editorContainer.appendChild(textarea);
    return editorContainer;
  }

  /**
   * 创建编辑控制按钮
   * @param {Element} messageElement - 消息元素
   * @returns {Element} 控制按钮容器
   */
  createEditControls(messageElement) {
    const controls = DOMUtils.createElement('div', {
      className: 'message-edit-controls'
    });

    const saveBtn = DOMUtils.createElement('button', {
      className: 'btn btn-primary btn-sm',
      textContent: '保存',
      title: 'Ctrl+Enter 保存'
    });

    const cancelBtn = DOMUtils.createElement('button', {
      className: 'btn btn-secondary btn-sm',
      textContent: '取消',
      title: 'Esc 取消'
    });

    // 绑定事件
    saveBtn.addEventListener('click', () => this.saveEdit(messageElement));
    cancelBtn.addEventListener('click', () => this.cancelEdit(messageElement));

    controls.appendChild(saveBtn);
    controls.appendChild(cancelBtn);

    return controls;
  }

  /**
   * 保存编辑（方案B：确保AI回复正确关联到新版本）
   * @param {Element} messageElement - 消息元素
   */
  saveEdit(messageElement) {
    const messageId = messageElement.dataset.messageId;
    const editor = messageElement.querySelector('.message-editor');
    const newContent = editor.value.trim();

    if (!newContent) {
      globalToolbarNotifications.notifyWarning(ToolbarActions.EDIT, '消息内容不能为空');
      return;
    }

    // 更新消息内容
    const messageIndex = this.messages.findIndex(m => m.id === messageId);
    if (messageIndex !== -1) {
      const message = this.messages[messageIndex];

      // 方案B：只允许编辑用户消息
      if (!message.isUser) {
        console.warn('方案B不允许编辑AI消息');
        globalToolbarNotifications.notifyWarning(ToolbarActions.EDIT, '无法编辑此消息');
        this.exitEditMode(messageElement);
        return;
      }

      const currentVersion = message.versions[message.currentVersionIndex];

      // 如果内容没有变化，直接退出编辑模式
      if (newContent === currentVersion.content) {
        this.exitEditMode(messageElement);
        return;
      }

      // 创建新版本（方案B：使用完整的版本结构）
      const newVersion = {
        id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        content: newContent,
        timestamp: Date.now(),
        aiReply: null, // 新版本初始没有AI回复
        metadata: {
          editReason: 'user_edit',
          editType: 'manual',
          previousVersionId: currentVersion.id,
          editedAt: Date.now()
        }
      };

      // 添加新版本到版本数组
      message.versions.push(newVersion);
      message.currentVersionIndex = message.versions.length - 1;
      message.edited = true;
      message.editedAt = Date.now();

      // 更新store
      this.store.setState('messages', this.messages);

      // 更新存储
      StorageUtils.saveChatHistory(this.messages);

      // 退出编辑模式并重新渲染整个消息
      this.exitEditMode(messageElement);
      this.refreshMessage(messageId);

      // 显示成功提示，包含版本信息
      const successMessage = `消息已更新 (版本 ${message.versions.length})`;
      globalToolbarNotifications.notifySuccess(ToolbarActions.EDIT, successMessage);

      // 触发AI重新回复（方案B：为新版本生成AI回复）
      this.regenerateAIReply(message);

      // 触发事件
      this.emit('messageUpdated', {
        message,
        newVersion,
        previousVersionIndex: message.versions.length - 2,
        currentVersionIndex: message.currentVersionIndex
      });

      console.log('消息编辑完成:', {
        messageId,
        newVersionId: newVersion.id,
        versionsCount: message.versions.length,
        contentLength: newContent.length
      });
    }
  }

  /**
   * 取消编辑
   * @param {Element} messageElement - 消息元素
   */
  cancelEdit(messageElement) {
    this.exitEditMode(messageElement);
    // globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '编辑已取消');
  }

  /**
   * 重新生成AI回复（直接更新版本中的aiReply）
   * @param {object} message - 用户消息对象
   */
  regenerateAIReply(message) {
    if (!message.isUser) {
      console.warn('只能为用户消息重新生成AI回复');
      return;
    }

    const currentVersion = this.getCurrentVersion(message);
    if (!currentVersion) {
      console.error('消息没有有效的当前版本');
      return;
    }

    // 清除当前版本的AI回复，准备重新生成
    if (currentVersion.aiReply) {
      // 保留一些元数据用于重试计数
      const retryCount = (currentVersion.aiReply.metadata?.retryCount || 0) + 1;

      // 重置AI回复
      currentVersion.aiReply = null;

      // 立即更新显示，移除AI回复
      this.removeAIReply(message.id);
    }

    // 触发新的AI回复生成
    this.globalEventManager.emit('message:regenerate-ai-reply', {
      userMessage: message,
      content: this.getCurrentVersionContent(message)
    });

    console.log('重新生成AI回复:', {
      messageId: message.id,
      content: this.getCurrentVersionContent(message)
    });
  }

  /**
   * 处理AI回复状态变化
   * @param {string} messageId - 消息ID
   * @param {string} status - 新状态
   * @param {object} updates - 更新数据
   */
  handleAIReplyStatusChange(messageId, status, updates = {}) {
    const message = this.messages.find(m => m.id === messageId);
    if (!message) {
      console.warn('未找到消息:', messageId);
      return;
    }

    const currentVersion = this.getCurrentVersion(message);
    if (!currentVersion || !currentVersion.aiReply) {
      console.warn('消息没有AI回复:', messageId);
      return;
    }

    // 更新AI回复状态
    const aiReply = currentVersion.aiReply;
    const oldStatus = aiReply.status;

    aiReply.status = status;
    aiReply.timestamp = Date.now();

    // 应用其他更新
    Object.assign(aiReply, updates);

    // 根据状态自动设置相关字段
    switch (status) {
      case 'generating':
        aiReply.startTimestamp = aiReply.startTimestamp || Date.now();
        aiReply.progress = Math.max(0, Math.min(99, updates.progress || aiReply.progress || 0));
        break;

      case 'completed':
        aiReply.endTimestamp = Date.now();
        aiReply.progress = 100;
        if (aiReply.startTimestamp) {
          aiReply.metadata.duration = aiReply.endTimestamp - aiReply.startTimestamp;
        }
        break;

      case 'error':
      case 'cancelled':
      case 'timeout':
        aiReply.endTimestamp = Date.now();
        if (aiReply.startTimestamp) {
          aiReply.metadata.duration = aiReply.endTimestamp - aiReply.startTimestamp;
        }
        break;
    }

    // 更新显示
    this.updateAIReplyStatus(messageId, aiReply);

    // 触发事件
    this.emit('aiReplyStatusChanged', {
      messageId,
      aiReplyId: aiReply.id,
      oldStatus,
      newStatus: status,
      aiReply
    });

    console.log('AI回复状态变化:', {
      messageId,
      oldStatus,
      newStatus: status,
      progress: aiReply.progress
    });
  }

  /**
   * 流式更新AI回复内容
   * @param {string} messageId - 消息ID
   * @param {string} contentChunk - 内容片段
   * @param {boolean} isComplete - 是否完成
   */
  streamAIReplyContent(messageId, contentChunk, isComplete = false) {
    const message = this.messages.find(m => m.id === messageId);
    if (!message) {
      console.warn('未找到消息:', messageId);
      return;
    }

    const currentVersion = this.getCurrentVersion(message);
    if (!currentVersion || !currentVersion.aiReply) {
      console.warn('消息没有AI回复:', messageId);
      return;
    }

    const aiReply = currentVersion.aiReply;

    // 追加内容
    aiReply.content += contentChunk;

    // 更新状态
    if (isComplete) {
      aiReply.status = 'completed';
      aiReply.progress = 100;
      aiReply.endTimestamp = Date.now();
      if (aiReply.startTimestamp) {
        aiReply.metadata.duration = aiReply.endTimestamp - aiReply.startTimestamp;
      }
    } else {
      aiReply.status = 'generating';
      // 根据内容长度估算进度
      const estimatedProgress = Math.min(95, Math.floor(aiReply.content.length / 10));
      aiReply.progress = estimatedProgress;
    }

    // 更新显示
    this.updateAIReplyStatus(messageId, aiReply);

    // 自动滚动到底部
    if (this.autoScroll) {
      this.scrollToBottom();
    }
  }

  /**
   * 获取消息的AI回复
   * @param {string} messageId - 消息ID
   * @returns {object|null} AI回复对象
   */
  getAIReply(messageId) {
    const message = this.messages.find(m => m.id === messageId);
    if (!message) {
      return null;
    }

    const currentVersion = this.getCurrentVersion(message);
    return currentVersion ? currentVersion.aiReply : null;
  }

  /**
   * 检查消息是否有活跃的AI回复
   * @param {string} messageId - 消息ID
   * @returns {boolean} 是否有活跃的AI回复
   */
  hasActiveAIReply(messageId) {
    const aiReply = this.getAIReply(messageId);
    if (!aiReply) {
      return false;
    }

    return ['pending', 'generating'].includes(aiReply.status);
  }

  /**
   * 获取所有活跃的AI回复
   * @returns {Array} 活跃的AI回复列表
   */
  getActiveAIReplies() {
    const activeReplies = [];

    this.messages.forEach(message => {
      if (message.isUser && this.hasActiveAIReply(message.id)) {
        const aiReply = this.getAIReply(message.id);
        activeReplies.push({
          messageId: message.id,
          aiReply: aiReply
        });
      }
    });

    return activeReplies;
  }

  /**
   * 取消所有活跃的AI回复
   */
  cancelAllActiveAIReplies() {
    const activeReplies = this.getActiveAIReplies();

    activeReplies.forEach(({ messageId, aiReply }) => {
      this.handleAIReplyStatusChange(messageId, 'cancelled', {
        content: '',
        progress: 0
      });
    });

    console.log(`已取消 ${activeReplies.length} 个活跃的AI回复`);
  }

  /**
   * 清理失败的AI回复
   * @param {string} messageId - 消息ID（可选，如果不提供则清理所有失败的回复）
   */
  cleanupFailedAIReplies(messageId = null) {
    const messagesToClean = messageId ?
      [this.messages.find(m => m.id === messageId)].filter(Boolean) :
      this.messages.filter(m => m.isUser);

    let cleanedCount = 0;

    messagesToClean.forEach(message => {
      const currentVersion = this.getCurrentVersion(message);
      if (currentVersion && currentVersion.aiReply) {
        const aiReply = currentVersion.aiReply;

        // 清理错误、取消或超时的AI回复
        if (['error', 'cancelled', 'timeout'].includes(aiReply.status)) {
          currentVersion.aiReply = null;
          this.removeAIReply(message.id);
          cleanedCount++;
        }
      }
    });

    if (cleanedCount > 0) {
      console.log(`已清理 ${cleanedCount} 个失败的AI回复`);
      // 更新存储
      this.store.setState('messages', this.messages);
      StorageUtils.saveChatHistory(this.messages);
    }
  }

  /**
   * 创建版本切换控件（优化版本控件）
   * @param {object} message - 消息对象
   * @returns {Element} 版本控件元素
   */
  createVersionControls(message) {
    const versionControls = DOMUtils.createElement('div', {
      className: 'message-version-controls',
      'data-message-id': message.id
    });

    const prevBtn = DOMUtils.createElement('button', {
      className: 'btn btn-icon version-btn version-prev-btn',
      title: '上一个版本 (Ctrl+←)',
      innerHTML: '◀'
    });

    const versionInfo = DOMUtils.createElement('div', {
      className: 'version-info'
    });

    // 版本计数器
    const versionCounter = DOMUtils.createElement('span', {
      className: 'version-counter',
      textContent: `${message.currentVersionIndex + 1} / ${message.versions.length}`
    });

    // 当前版本的AI回复状态指示器
    const currentVersion = message.versions[message.currentVersionIndex];
    const aiReplyIndicator = DOMUtils.createElement('span', {
      className: 'version-ai-indicator'
    });

    if (currentVersion.aiReply) {
      const status = currentVersion.aiReply.status;
      let indicator = '';
      switch (status) {
        case 'completed':
          indicator = '🤖';
          aiReplyIndicator.title = 'AI回复已完成';
          break;
        case 'generating':
          indicator = '⏳';
          aiReplyIndicator.title = 'AI回复生成中';
          break;
        case 'error':
          indicator = '❌';
          aiReplyIndicator.title = 'AI回复生成失败';
          break;
        case 'pending':
          indicator = '⏳';
          aiReplyIndicator.title = 'AI回复等待中';
          break;
        default:
          indicator = '🤖';
          aiReplyIndicator.title = 'AI回复';
      }
      aiReplyIndicator.textContent = indicator;
    } else {
      aiReplyIndicator.textContent = '💬';
      aiReplyIndicator.title = '仅用户消息';
    }

    versionInfo.appendChild(versionCounter);
    versionInfo.appendChild(aiReplyIndicator);

    const nextBtn = DOMUtils.createElement('button', {
      className: 'btn btn-icon version-btn version-next-btn',
      title: '下一个版本 (Ctrl+→)',
      innerHTML: '▶'
    });

    // 设置按钮禁用状态
    const isFirst = message.currentVersionIndex === 0;
    const isLast = message.currentVersionIndex === message.versions.length - 1;

    if (isFirst) {
      prevBtn.disabled = true;
      prevBtn.classList.add('disabled');
    }

    if (isLast) {
      nextBtn.disabled = true;
      nextBtn.classList.add('disabled');
    }

    // 绑定事件
    prevBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.switchVersion(message, -1);
    });

    nextBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.switchVersion(message, 1);
    });

    // 键盘快捷键支持
    versionControls.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        if (e.key === 'ArrowLeft' && !isFirst) {
          e.preventDefault();
          this.switchVersion(message, -1);
        } else if (e.key === 'ArrowRight' && !isLast) {
          e.preventDefault();
          this.switchVersion(message, 1);
        }
      }
    });

    versionControls.appendChild(prevBtn);
    versionControls.appendChild(versionInfo);
    versionControls.appendChild(nextBtn);

    return versionControls;
  }

  /**
   * 切换消息版本（简化版本切换逻辑）
   * @param {object} message - 消息对象
   * @param {number} direction - 方向 (-1: 上一个, 1: 下一个)
   */
  switchVersion(message, direction) {
    if (!message.isUser) {
      console.warn('只支持用户消息的版本切换');
      return;
    }

    const newIndex = message.currentVersionIndex + direction;

    if (newIndex < 0 || newIndex >= message.versions.length) {
      return;
    }

    const oldIndex = message.currentVersionIndex;
    const oldVersion = message.versions[oldIndex];
    const newVersion = message.versions[newIndex];

    // 更新当前版本索引
    message.currentVersionIndex = newIndex;

    // 直接重新渲染整个消息，包括内嵌的AI回复
    this.refreshMessage(message.id);

    // 更新存储
    this.store.setState('messages', this.messages);
    StorageUtils.saveChatHistory(this.messages);

    // 触发版本切换事件
    this.emit('versionSwitched', {
      messageId: message.id,
      oldIndex,
      newIndex,
      oldVersion,
      newVersion,
      hasAIReply: !!newVersion.aiReply
    });

    console.log('版本切换:', {
      messageId: message.id,
      from: `${oldIndex + 1}/${message.versions.length}`,
      to: `${newIndex + 1}/${message.versions.length}`,
      hasAIReply: !!newVersion.aiReply
    });

    // 显示版本切换提示
    // globalToolbarNotifications.notifyInfo(ToolbarActions.EDIT, `已切换到版本 ${newIndex + 1}`);
  }

  /**
   * 切换版本时的AI回复处理（已废弃）
   * 在中，AI回复内嵌在版本中，版本切换时会自动显示对应的AI回复
   * 此方法保留用于向后兼容，但不再执行任何操作
   * @deprecated 中AI回复内嵌在版本中，不需要单独处理
   */
  switchToCorrespondingAIReply(userMessage, currentVersion) {
    console.warn('switchToCorrespondingAIReply已废弃，中AI回复内嵌在版本中');
    // 中不需要此方法，AI回复会随版本切换自动更新
  }

  /**
   * 为消息添加版本控件
   * @param {Element} messageElement - 消息元素
   * @param {object} message - 消息对象
   */
  addVersionControlsToMessage(messageElement, message) {
    const messageActions = messageElement.querySelector('.message-actions');
    const editBtn = messageActions.querySelector('.message-edit-btn');

    if (messageActions && editBtn) {
      const versionControls = this.createVersionControls(message);
      messageActions.insertBefore(versionControls, editBtn);
    }
  }

  /**
   * 更新版本控件（支持新的版本控件结构）
   * @param {Element} messageElement - 消息元素
   * @param {object} message - 消息对象
   */
  updateVersionControls(messageElement, message) {
    const versionControls = messageElement.querySelector('.message-version-controls');
    if (!versionControls) {
      return;
    }

    // 更新版本计数器
    const versionCounter = versionControls.querySelector('.version-counter');
    if (versionCounter) {
      versionCounter.textContent = `${message.currentVersionIndex + 1} / ${message.versions.length}`;
    }

    // 更新AI回复指示器
    const aiReplyIndicator = versionControls.querySelector('.version-ai-indicator');
    if (aiReplyIndicator) {
      const currentVersion = message.versions[message.currentVersionIndex];

      if (currentVersion.aiReply) {
        const status = currentVersion.aiReply.status;
        let indicator = '';
        let title = '';

        switch (status) {
          case 'completed':
            indicator = '🤖';
            title = 'AI回复已完成';
            break;
          case 'generating':
            indicator = '⏳';
            title = 'AI回复生成中';
            break;
          case 'error':
            indicator = '❌';
            title = 'AI回复生成失败';
            break;
          case 'pending':
            indicator = '⏳';
            title = 'AI回复等待中';
            break;
          case 'cancelled':
            indicator = '⏹️';
            title = 'AI回复已取消';
            break;
          case 'timeout':
            indicator = '⏰';
            title = 'AI回复超时';
            break;
          default:
            indicator = '🤖';
            title = 'AI回复';
        }

        aiReplyIndicator.textContent = indicator;
        aiReplyIndicator.title = title;
      } else {
        aiReplyIndicator.textContent = '💬';
        aiReplyIndicator.title = '仅用户消息';
      }
    }

    // 更新按钮状态
    const prevBtn = versionControls.querySelector('.version-prev-btn');
    const nextBtn = versionControls.querySelector('.version-next-btn');

    const isFirst = message.currentVersionIndex === 0;
    const isLast = message.currentVersionIndex === message.versions.length - 1;

    if (prevBtn) {
      prevBtn.disabled = isFirst;
      if (isFirst) {
        prevBtn.classList.add('disabled');
        prevBtn.setAttribute('disabled', 'disabled');
      } else {
        prevBtn.classList.remove('disabled');
        prevBtn.removeAttribute('disabled');
      }
    }

    if (nextBtn) {
      nextBtn.disabled = isLast;
      if (isLast) {
        nextBtn.classList.add('disabled');
        nextBtn.setAttribute('disabled', 'disabled');
      } else {
        nextBtn.classList.remove('disabled');
        nextBtn.removeAttribute('disabled');
      }
    }

    console.log('版本控件已更新:', {
      messageId: message.id,
      currentVersion: `${message.currentVersionIndex + 1}/${message.versions.length}`,
      hasAIReply: !!message.versions[message.currentVersionIndex].aiReply
    });
  }

  /**
   * 跳转到指定版本
   * @param {object} message - 消息对象
   * @param {number} targetIndex - 目标版本索引
   */
  jumpToVersion(message, targetIndex) {
    if (!message.isUser) {
      console.warn('只支持用户消息的版本跳转');
      return;
    }

    if (targetIndex < 0 || targetIndex >= message.versions.length) {
      console.warn('版本索引超出范围:', targetIndex);
      return;
    }

    if (targetIndex === message.currentVersionIndex) {
      return; // 已经是当前版本
    }

    const oldIndex = message.currentVersionIndex;
    message.currentVersionIndex = targetIndex;

    // 重新渲染消息
    this.refreshMessage(message.id);

    // 更新存储
    this.store.setState('messages', this.messages);
    StorageUtils.saveChatHistory(this.messages);

    // 触发事件
    this.emit('versionJumped', {
      messageId: message.id,
      oldIndex,
      newIndex: targetIndex,
      version: message.versions[targetIndex]
    });

    console.log('版本跳转:', {
      messageId: message.id,
      from: oldIndex + 1,
      to: targetIndex + 1
    });
  }

  /**
   * 获取版本历史信息
   * @param {object} message - 消息对象
   * @returns {Array} 版本历史信息
   */
  getVersionHistory(message) {
    if (!message.versions) {
      return [];
    }

    return message.versions.map((version, index) => ({
      index,
      id: version.id,
      content: version.content,
      timestamp: version.timestamp,
      isCurrent: index === message.currentVersionIndex,
      hasAIReply: !!version.aiReply,
      aiReplyStatus: version.aiReply ? version.aiReply.status : null,
      metadata: version.metadata || {},
      formattedTime: this.formatTime(version.timestamp),
      contentPreview: version.content.length > 50 ?
        version.content.substring(0, 50) + '...' :
        version.content
    }));
  }

  /**
   * 删除指定版本
   * @param {object} message - 消息对象
   * @param {number} versionIndex - 要删除的版本索引
   */
  deleteVersion(message, versionIndex) {
    if (!message.isUser) {
      console.warn('只支持用户消息的版本删除');
      return false;
    }

    if (message.versions.length <= 1) {
      console.warn('不能删除最后一个版本');
      return false;
    }

    if (versionIndex < 0 || versionIndex >= message.versions.length) {
      console.warn('版本索引超出范围:', versionIndex);
      return false;
    }

    // 删除版本
    const deletedVersion = message.versions.splice(versionIndex, 1)[0];

    // 调整当前版本索引
    if (message.currentVersionIndex >= versionIndex) {
      message.currentVersionIndex = Math.max(0, message.currentVersionIndex - 1);
    }

    // 重新渲染消息
    this.refreshMessage(message.id);

    // 更新存储
    this.store.setState('messages', this.messages);
    StorageUtils.saveChatHistory(this.messages);

    // 触发事件
    this.emit('versionDeleted', {
      messageId: message.id,
      deletedIndex: versionIndex,
      deletedVersion,
      currentIndex: message.currentVersionIndex,
      remainingVersions: message.versions.length
    });

    console.log('版本已删除:', {
      messageId: message.id,
      deletedIndex: versionIndex,
      currentIndex: message.currentVersionIndex,
      remainingVersions: message.versions.length
    });

    return true;
  }

  /**
   * 复制版本
   * @param {object} message - 消息对象
   * @param {number} versionIndex - 要复制的版本索引
   */
  duplicateVersion(message, versionIndex) {
    if (!message.isUser) {
      console.warn('只支持用户消息的版本复制');
      return false;
    }

    if (versionIndex < 0 || versionIndex >= message.versions.length) {
      console.warn('版本索引超出范围:', versionIndex);
      return false;
    }

    const sourceVersion = message.versions[versionIndex];
    const newVersion = {
      id: `ver_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      content: sourceVersion.content,
      timestamp: Date.now(),
      aiReply: null, // 复制的版本不包含AI回复
      metadata: {
        ...sourceVersion.metadata,
        duplicatedFrom: sourceVersion.id,
        duplicatedAt: Date.now()
      }
    };

    // 在当前版本后插入新版本
    const insertIndex = message.currentVersionIndex + 1;
    message.versions.splice(insertIndex, 0, newVersion);
    message.currentVersionIndex = insertIndex;

    // 重新渲染消息
    this.refreshMessage(message.id);

    // 更新存储
    this.store.setState('messages', this.messages);
    StorageUtils.saveChatHistory(this.messages);

    // 触发事件
    this.emit('versionDuplicated', {
      messageId: message.id,
      sourceIndex: versionIndex,
      newIndex: insertIndex,
      newVersion
    });

    console.log('版本已复制:', {
      messageId: message.id,
      sourceIndex: versionIndex,
      newIndex: insertIndex
    });

    return true;
  }

  /**
   * 退出编辑模式
   * @param {Element} messageElement - 消息元素
   * @param {string} newContent - 新内容（可选）
   */
  exitEditMode(messageElement, newContent = null) {
    const messageText = messageElement.querySelector('.message-text');
    const editorContainer = messageElement.querySelector('.message-editor-container');
    const editControls = messageElement.querySelector('.message-edit-controls');

    // 移除编辑器和控制按钮
    if (editorContainer) {
      editorContainer.remove();
    }
    if (editControls) {
      editControls.remove();
    }

    // 更新文本内容
    if (newContent !== null) {
      messageText.textContent = newContent;
    }

    // 显示原始文本
    messageText.style.display = '';

    // 移除编辑状态
    messageElement.classList.remove('editing');
    delete messageElement.dataset.originalContent;
  }

  /**
   * 调整编辑器高度
   * @param {Element} textarea - 文本域元素
   */
  adjustEditorHeight(textarea) {
    textarea.style.height = 'auto';
    const scrollHeight = textarea.scrollHeight;
    const maxHeight = window.innerHeight * 0.3; // 最大30%屏幕高度

    if (scrollHeight > maxHeight) {
      textarea.style.height = maxHeight + 'px';
      textarea.style.overflowY = 'auto';
    } else {
      textarea.style.height = Math.max(scrollHeight, 60) + 'px'; // 最小60px
      textarea.style.overflowY = 'hidden';
    }
  }

  /**
   * 处理删除消息
   * @param {string} messageId - 消息ID
   */
  handleDeleteMessage(messageId) {
    const messageIndex = this.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) {return;}

    // 显示确认对话框
    globalToolbarNotifications.notifyConfirm(
      ToolbarActions.DELETE,
      () => {
        // 确认删除
        this.performDeleteMessage(messageId, messageIndex);
      },
      () => {
        // 取消删除
        // globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '删除操作已取消');
      },
      '确定要删除这条消息吗？此操作不可撤销。'
    );
  }

  /**
   * 执行删除消息（方案B：只删除用户消息，AI回复自动删除）
   * @param {string} messageId - 消息ID
   * @param {number} messageIndex - 消息索引
   */
  performDeleteMessage(messageId, messageIndex) {
    try {
      const messageToDelete = this.messages[messageIndex];

      // 方案B：检查是否为用户消息
      if (!messageToDelete.isUser) {
        console.warn('方案B不应该删除独立的AI消息');
        globalToolbarNotifications.notifyWarning(ToolbarActions.DELETE, '无法删除此消息');
        return;
      }

      // 统计删除信息
      const deleteInfo = this.analyzeMessageDeletion(messageToDelete);

      // 从数组中移除
      const deletedMessage = this.messages.splice(messageIndex, 1)[0];

      // 更新存储
      this.store.setState('messages', this.messages);
      StorageUtils.saveChatHistory(this.messages);

      // 从DOM中移除（带动画）
      const messageElement = this.messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
      if (messageElement) {
        this.animateMessageExit(messageElement).then(() => {
          messageElement.remove();
        });
      }

      // 显示成功通知，包含删除的详细信息
      const successMessage = deleteInfo.hasAIReplies ?
        `消息已删除（包含${deleteInfo.aiReplyCount}个AI回复）` :
        '消息已删除';

      globalToolbarNotifications.notifySuccess(ToolbarActions.DELETE, successMessage);

      // 触发删除事件
      this.emit('messageDeleted', {
        message: deletedMessage,
        deleteInfo
      });

      console.log('消息删除成功:', {
        messageId,
        versionsCount: deleteInfo.versionsCount,
        aiReplyCount: deleteInfo.aiReplyCount,
        hasAIReplies: deleteInfo.hasAIReplies
      });

    } catch (error) {
      console.error('Failed to delete message:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.DELETE, '删除失败，请重试');
    }
  }

  /**
   * 分析消息删除的影响
   * @param {object} message - 要删除的消息
   * @returns {object} 删除分析信息
   */
  analyzeMessageDeletion(message) {
    const analysis = {
      versionsCount: message.versions ? message.versions.length : 0,
      aiReplyCount: 0,
      hasAIReplies: false,
      activeAIReplies: 0,
      completedAIReplies: 0
    };

    if (message.versions) {
      message.versions.forEach(version => {
        if (version.aiReply) {
          analysis.aiReplyCount++;
          analysis.hasAIReplies = true;

          if (version.aiReply.status === 'completed') {
            analysis.completedAIReplies++;
          } else if (['pending', 'generating'].includes(version.aiReply.status)) {
            analysis.activeAIReplies++;
          }
        }
      });
    }

    return analysis;
  }

  /**
   * 复制消息（方案B：支持复制包含AI回复的完整内容）
   * @param {Element} messageElement - 消息元素
   */
  async copyMessage(messageElement) {
    const messageId = messageElement.dataset.messageId;
    const message = this.messages.find(m => m.id === messageId);

    if (!message) {
      console.error('未找到消息:', messageId);
      return;
    }

    try {
      let contentToCopy = '';

      if (message.isUser) {
        // 方案B：复制用户消息和AI回复的完整内容
        const currentVersion = this.getCurrentVersion(message);
        if (currentVersion) {
          // 用户消息内容
          contentToCopy = `用户: ${currentVersion.content}`;

          // 如果有AI回复，也包含在复制内容中
          if (currentVersion.aiReply && currentVersion.aiReply.content) {
            const aiReply = currentVersion.aiReply;
            contentToCopy += `\n\nAI回复: ${aiReply.content}`;

            // 如果有元数据，也可以包含
            if (aiReply.metadata && (aiReply.metadata.model || aiReply.metadata.tokens)) {
              const metadata = [];
              if (aiReply.metadata.model) metadata.push(`模型: ${aiReply.metadata.model}`);
              if (aiReply.metadata.tokens) metadata.push(`Token: ${aiReply.metadata.tokens}`);
              if (aiReply.metadata.duration) {
                const duration = (aiReply.metadata.duration / 1000).toFixed(1);
                metadata.push(`耗时: ${duration}s`);
              }

              if (metadata.length > 0) {
                contentToCopy += `\n(${metadata.join(', ')})`;
              }
            }
          }
        }
      } else {
        // 兼容性：如果是独立AI消息（不应该在方案B中出现）
        console.warn('方案B不应该有独立的AI消息');
        const messageText = messageElement.querySelector('.message-text');
        contentToCopy = messageText ? messageText.textContent : '';
      }

      if (!contentToCopy) {
        globalToolbarNotifications.notifyWarning(ToolbarActions.COPY, '没有内容可复制');
        return;
      }

      await DOMUtils.copyToClipboard(contentToCopy);

      // 显示成功提示，包含复制的内容类型
      const hasAIReply = message.isUser &&
        this.getCurrentVersion(message)?.aiReply?.content;
      const successMessage = hasAIReply ?
        '已复制消息和AI回复' :
        '已复制消息内容';

      globalToolbarNotifications.notifySuccess(ToolbarActions.COPY, successMessage);

      console.log('消息复制成功:', {
        messageId,
        hasAIReply,
        contentLength: contentToCopy.length
      });

    } catch (error) {
      console.error('Failed to copy message:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.COPY, '复制失败，请重试');
    }
  }

  /**
   * 清空消息
   */
  clearMessages() {
    if (this.messages.length === 0) {
      globalToolbarNotifications.notifyInfo(ToolbarActions.DELETE, '没有消息需要清空');
      return;
    }

    // 显示确认对话框
    globalToolbarNotifications.notifyConfirm(
      ToolbarActions.RESET,
      () => {
        // 确认清空
        this.performClearMessages();
      },
      () => {
        // 取消清空
        // globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '清空操作已取消');
      },
      `确定要清空所有 ${this.messages.length} 条消息吗？此操作不可撤销。`
    );
  }

  /**
   * 执行清空消息
   */
  performClearMessages() {
    try {
      const messageCount = this.messages.length;

      this.messages = [];
      this.store.setState('messages', []);
      StorageUtils.clearChatHistory();

      if (this.messagesContainer) {
        this.messagesContainer.innerHTML = '';
      }

      globalToolbarNotifications.notifySuccess(ToolbarActions.RESET, `已清空 ${messageCount} 条消息`);

      this.emit('messagesCleared');
    } catch (error) {
      console.error('Failed to clear messages:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.RESET, '清空失败，请重试');
    }
  }

  /**
   * 导出消息
   * @param {Array} messages - 消息数组（可选）
   * @returns {string} 导出的文本
   */
  exportMessages(messages = null) {
    const messagesToExport = messages || this.messages;

    if (messagesToExport.length === 0) {
      globalToolbarNotifications.notifyWarning(ToolbarActions.EXPORT, '没有消息可以导出');
      return '';
    }

    try {
      // 显示导出中通知
      const loadingId = globalToolbarNotifications.notifyLoading(ToolbarActions.EXPORT, '正在导出聊天记录...');

      let exportData = '聊天记录导出\n';
      exportData += `导出时间: ${new Date().toLocaleString('zh-CN')}\n\n`;

      messagesToExport.forEach(message => {
        const time = this.formatTime(message.timestamp);
        const sender = message.isUser ? '用户' : 'AI';
        const content = this.getCurrentVersionContent(message);
        exportData += `[${time}] ${sender}: ${content}\n`;
      });

      // 模拟导出延迟
      setTimeout(() => {
        globalToolbarNotifications.hide(loadingId);
        globalToolbarNotifications.notifySuccess(ToolbarActions.EXPORT, `成功导出 ${messagesToExport.length} 条消息`);
      }, 500);

      return exportData;
    } catch (error) {
      console.error('Export failed:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.EXPORT, '导出失败，请重试');
      return '';
    }
  }

  /**
   * 消息进入动画
   * @param {Element} messageElement - 消息元素
   * @param {boolean} isUser - 是否为用户消息
   */
  animateMessageEntry(messageElement, isUser) {
    // 检查是否应该减少动画
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      return;
    }

    // 根据消息类型选择不同的动画
    const animationType = isUser ? AnimationTypes.SLIDE_IN_RIGHT : AnimationTypes.SLIDE_IN_LEFT;

    // 设置初始状态
    messageElement.style.opacity = '0';
    messageElement.style.transform = isUser ? 'translateX(50px)' : 'translateX(-50px)';

    // 执行动画
    globalAnimationManager.animate(messageElement, animationType, {
      duration: 300,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      onComplete: () => {
        // 清除内联样式，让CSS接管
        messageElement.style.opacity = '';
        messageElement.style.transform = '';
      }
    }).catch(() => {
      // 动画失败时确保元素可见
      messageElement.style.opacity = '';
      messageElement.style.transform = '';
    });
  }

  /**
   * 消息退出动画
   * @param {Element} messageElement - 消息元素
   * @returns {Promise} 动画完成的Promise
   */
  animateMessageExit(messageElement) {
    // 检查是否应该减少动画
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      return Promise.resolve();
    }

    // 执行退出动画
    return globalAnimationManager.animate(messageElement, AnimationTypes.FADE_OUT, {
      duration: 200,
      easing: 'ease-in'
    }).catch(() => {
      // 动画失败时也要继续
      return Promise.resolve();
    });
  }

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    // 滚动的是外层的messages-container，而不是内层的messages-wrapper
    const scrollContainer = this.element;
    if (scrollContainer) {
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
    }
  }

  /**
   * 格式化时间
   * @param {number} timestamp - 时间戳
   * @returns {string} 格式化的时间
   */
  formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * 生成消息ID
   * @returns {string} 消息ID
   */
  generateMessageId() {
    return `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 搜索消息（方案B：支持搜索版本中的AI回复内容）
   * @param {string} query - 搜索查询
   * @param {object} options - 搜索选项
   * @returns {Array} 搜索结果
   */
  searchMessages(query, options = {}) {
    if (!query || !query.trim()) {
      globalToolbarNotifications.notifyWarning(ToolbarActions.SEARCH, '请输入搜索关键词');
      return [];
    }

    try {
      // 显示搜索中通知
      const loadingId = globalToolbarNotifications.notifyLoading(ToolbarActions.SEARCH, '正在搜索消息...');

      // 执行搜索（方案B：搜索用户消息和AI回复）
      const results = this.performAdvancedSearch(query, options);

      // 隐藏加载通知
      setTimeout(() => {
        globalToolbarNotifications.hide(loadingId);

        // 显示搜索结果
        if (results.length === 0) {
          globalToolbarNotifications.notifyInfo(ToolbarActions.SEARCH, `未找到包含"${query}"的消息`);
        } else {
          const userMatches = results.filter(r => r.type === 'user').length;
          const aiMatches = results.filter(r => r.type === 'ai').length;
          const resultText = `找到 ${results.length} 条匹配 (用户消息: ${userMatches}, AI回复: ${aiMatches})`;
          globalToolbarNotifications.notifySuccess(ToolbarActions.SEARCH, resultText);
        }
      }, 300);

      return results;
    } catch (error) {
      console.error('Search failed:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.SEARCH, '搜索失败，请重试');
      return [];
    }
  }

  /**
   * 执行高级搜索（方案B：搜索版本和AI回复）
   * @param {string} query - 搜索查询
   * @param {object} options - 搜索选项
   * @returns {Array} 搜索结果
   */
  performAdvancedSearch(query, options = {}) {
    const {
      caseSensitive = false,
      includeAIReplies = true,
      includeMetadata = false,
      searchAllVersions = false
    } = options;

    const searchQuery = caseSensitive ? query : query.toLowerCase();
    const results = [];

    this.messages.forEach(message => {
      if (!message.isUser) {
        // 方案B：跳过独立AI消息（不应该存在）
        console.warn('发现独立AI消息，跳过搜索:', message.id);
        return;
      }

      // 搜索版本内容
      const versionsToSearch = searchAllVersions ?
        message.versions :
        [message.versions[message.currentVersionIndex]];

      versionsToSearch.forEach((version, versionIndex) => {
        if (!version) return;

        // 搜索用户消息内容
        const userContent = caseSensitive ? version.content : version.content.toLowerCase();
        if (userContent.includes(searchQuery)) {
          results.push({
            type: 'user',
            messageId: message.id,
            versionId: version.id,
            versionIndex: searchAllVersions ? versionIndex : message.currentVersionIndex,
            content: version.content,
            timestamp: version.timestamp,
            matchType: 'content',
            preview: this.createSearchPreview(version.content, query)
          });
        }

        // 搜索AI回复内容
        if (includeAIReplies && version.aiReply && version.aiReply.content) {
          const aiContent = caseSensitive ?
            version.aiReply.content :
            version.aiReply.content.toLowerCase();

          if (aiContent.includes(searchQuery)) {
            results.push({
              type: 'ai',
              messageId: message.id,
              versionId: version.id,
              versionIndex: searchAllVersions ? versionIndex : message.currentVersionIndex,
              aiReplyId: version.aiReply.id,
              content: version.aiReply.content,
              timestamp: version.aiReply.timestamp,
              matchType: 'ai_reply',
              preview: this.createSearchPreview(version.aiReply.content, query),
              aiStatus: version.aiReply.status
            });
          }
        }

        // 搜索元数据
        if (includeMetadata) {
          const metadata = JSON.stringify(version.metadata || {});
          const metadataContent = caseSensitive ? metadata : metadata.toLowerCase();

          if (metadataContent.includes(searchQuery)) {
            results.push({
              type: 'metadata',
              messageId: message.id,
              versionId: version.id,
              versionIndex: searchAllVersions ? versionIndex : message.currentVersionIndex,
              content: metadata,
              timestamp: version.timestamp,
              matchType: 'metadata',
              preview: this.createSearchPreview(metadata, query)
            });
          }
        }
      });
    });

    // 按时间戳排序
    results.sort((a, b) => b.timestamp - a.timestamp);

    console.log('搜索完成:', {
      query,
      totalResults: results.length,
      userMatches: results.filter(r => r.type === 'user').length,
      aiMatches: results.filter(r => r.type === 'ai').length,
      metadataMatches: results.filter(r => r.type === 'metadata').length
    });

    return results;
  }

  /**
   * 创建搜索预览
   * @param {string} content - 内容
   * @param {string} query - 查询词
   * @returns {string} 预览文本
   */
  createSearchPreview(content, query) {
    const maxLength = 100;
    const queryIndex = content.toLowerCase().indexOf(query.toLowerCase());

    if (queryIndex === -1) {
      return content.length > maxLength ?
        content.substring(0, maxLength) + '...' :
        content;
    }

    const start = Math.max(0, queryIndex - 30);
    const end = Math.min(content.length, queryIndex + query.length + 30);

    let preview = content.substring(start, end);
    if (start > 0) preview = '...' + preview;
    if (end < content.length) preview = preview + '...';

    return preview;
  }

  /**
   * 获取消息统计
   * @returns {object} 统计信息
   */
  getStatistics() {
    return this.messageManager.getStatistics();
  }

  /**
   * 批量删除消息
   * @param {Array} messageIds - 消息ID数组
   * @returns {number} 删除的消息数量
   */
  batchDeleteMessages(messageIds) {
    if (!messageIds || messageIds.length === 0) {
      globalToolbarNotifications.notifyWarning(ToolbarActions.DELETE, '没有选择要删除的消息');
      return 0;
    }

    // 显示确认对话框
    globalToolbarNotifications.notifyConfirm(
      ToolbarActions.DELETE,
      () => {
        // 确认批量删除
        this.performBatchDelete(messageIds);
      },
      () => {
        // 取消批量删除
        // globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '批量删除已取消');
      },
      `确定要删除选中的 ${messageIds.length} 条消息吗？此操作不可撤销。`
    );

    return 0; // 实际删除在确认后执行
  }

  /**
   * 执行批量删除
   * @param {Array} messageIds - 消息ID数组
   */
  performBatchDelete(messageIds) {
    try {
      const total = messageIds.length;
      let completed = 0;
      let failed = 0;

      // 显示进度通知
      globalToolbarNotifications.notifyBatch('删除', total, completed, failed);

      // 模拟批量处理过程
      const processNext = () => {
        if (completed + failed < total) {
          const currentId = messageIds[completed + failed];

          try {
            // 尝试删除单个消息
            const messageIndex = this.messages.findIndex(m => m.id === currentId);
            if (messageIndex !== -1) {
              this.messages.splice(messageIndex, 1);

              // 从DOM中移除
              const messageElement = this.messagesContainer.querySelector(`[data-message-id="${currentId}"]`);
              if (messageElement) {
                messageElement.remove();
              }

              completed++;
            } else {
              failed++;
            }
          } catch (error) {
            console.error(`Failed to delete message ${currentId}:`, error);
            failed++;
          }

          // 更新进度
          globalToolbarNotifications.notifyBatch('删除', total, completed, failed);

          // 继续处理下一个
          setTimeout(processNext, 50);
        } else {
          // 批量删除完成
          this.finalizeBatchDelete(total, completed, failed);
        }
      };

      // 开始处理
      processNext();

    } catch (error) {
      console.error('Batch delete failed:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.DELETE, '批量删除失败，请重试');
    }
  }

  /**
   * 完成批量删除
   * @param {number} total - 总数
   * @param {number} completed - 完成数
   * @param {number} failed - 失败数
   */
  finalizeBatchDelete(total, completed, failed) {
    // 更新存储
    this.store.setState('messages', this.messages);
    StorageUtils.saveChatHistory(this.messages);

    // 显示最终结果
    if (failed === 0) {
      globalToolbarNotifications.notifySuccess(ToolbarActions.DELETE, `成功删除 ${completed} 条消息`);
    } else if (completed === 0) {
      globalToolbarNotifications.notifyError(ToolbarActions.DELETE, `删除失败，${failed} 条消息无法删除`);
    } else {
      globalToolbarNotifications.notifyWarning(ToolbarActions.DELETE, `删除完成：成功 ${completed} 条，失败 ${failed} 条`);
    }

    this.emit('batchDeleted', { total, completed, failed });
  }

  /**
   * 高亮搜索结果
   * @param {string} query - 搜索查询
   */
  highlightSearchResults(query) {
    if (!query.trim()) {
      this.clearHighlights();
      return;
    }

    const messageElements = this.messagesContainer.querySelectorAll('.message-text');
    messageElements.forEach(element => {
      const text = element.textContent;
      const highlightedText = text.replace(
        new RegExp(`(${query})`, 'gi'),
        '<mark>$1</mark>'
      );
      element.innerHTML = highlightedText;
    });
  }

  /**
   * 清除高亮
   */
  clearHighlights() {
    const messageElements = this.messagesContainer.querySelectorAll('.message-text');
    messageElements.forEach(element => {
      const text = element.textContent;
      element.innerHTML = '';
      element.textContent = text;
    });
  }

  /**
   * 导出消息（增强版）
   * @param {string} format - 导出格式
   * @param {Array} messageIds - 要导出的消息ID（可选）
   * @returns {string} 导出的数据
   */
  exportMessagesAdvanced(format = 'json', messageIds = null) {
    try {
      let messagesToExport;

      if (messageIds) {
        messagesToExport = this.messages.filter(m => messageIds.includes(m.id));
        if (messagesToExport.length === 0) {
          globalToolbarNotifications.notifyWarning(ToolbarActions.EXPORT, '没有选中的消息可以导出');
          return '';
        }
      } else {
        messagesToExport = this.messages;
        if (messagesToExport.length === 0) {
          globalToolbarNotifications.notifyWarning(ToolbarActions.EXPORT, '没有消息可以导出');
          return '';
        }
      }

      // 显示导出中通知
      const loadingId = globalToolbarNotifications.notifyLoading(ToolbarActions.EXPORT, `正在导出${format.toUpperCase()}格式...`);

      const result = messageIds
        ? this.messageManager.exportMessages(format, messagesToExport)
        : this.messageManager.exportMessages(format);

      // 模拟导出延迟
      setTimeout(() => {
        globalToolbarNotifications.hide(loadingId);
        globalToolbarNotifications.notifySuccess(ToolbarActions.EXPORT, `成功导出 ${messagesToExport.length} 条消息为${format.toUpperCase()}格式`);
      }, 800);

      return result;
    } catch (error) {
      console.error('Advanced export failed:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.EXPORT, `${format.toUpperCase()}格式导出失败，请重试`);
      return '';
    }
  }

  /**
   * 销毁前钩子
   */
  beforeDestroy() {
    // 移除全局事件监听
    this.globalEventManager.off('message:add', this.addMessage);
    this.globalEventManager.off('message:edit', this.editMessage);
    this.globalEventManager.off('message:delete', this.deleteMessage);
    this.globalEventManager.off('message:clear', this.clearMessages);
  }
}

export default MessageComponent;
