/**
 * 数据迁移工具
 * 用于将旧版本的消息数据结构迁移到新的方案B结构
 */

import { 
  createMessage, 
  createMessageVersion, 
  createAIReply, 
  validateMessage,
  AIReplyStatus 
} from '../types/message-types.js';

/**
 * 数据迁移主函数
 * @param {Array} oldMessages - 旧格式的消息数组
 * @returns {Object} 迁移结果
 */
export function migrateMessagesToNewFormat(oldMessages) {
  console.log('开始数据迁移，旧消息数量:', oldMessages.length);
  
  const migrationResult = {
    messages: [],
    statistics: {
      totalMessages: oldMessages.length,
      userMessages: 0,
      aiReplies: 0,
      migratedVersions: 0,
      orphanedAIMessages: 0
    },
    warnings: [],
    errors: []
  };

  try {
    // 创建备份
    const backup = createBackup(oldMessages);
    console.log('已创建数据备份');

    // 执行迁移
    const newMessages = performMigration(oldMessages, migrationResult);
    
    // 验证迁移结果
    const validationResults = validateMigratedData(newMessages);
    migrationResult.messages = newMessages;
    migrationResult.warnings.push(...validationResults.warnings);
    migrationResult.errors.push(...validationResults.errors);

    console.log('数据迁移完成，统计信息:', migrationResult.statistics);
    
    return migrationResult;
  } catch (error) {
    migrationResult.errors.push(`迁移过程中发生错误: ${error.message}`);
    console.error('数据迁移失败:', error);
    return migrationResult;
  }
}

/**
 * 执行实际的数据迁移
 * @param {Array} oldMessages - 旧消息数组
 * @param {Object} migrationResult - 迁移结果对象
 * @returns {Array} 新格式的消息数组
 */
function performMigration(oldMessages, migrationResult) {
  const newMessages = [];
  
  for (let i = 0; i < oldMessages.length; i++) {
    const message = oldMessages[i];
    
    // 只处理用户消息，AI消息将被合并到用户消息中
    if (message.isUser) {
      try {
        const migratedMessage = migrateUserMessage(message, oldMessages, i, migrationResult);
        newMessages.push(migratedMessage);
        migrationResult.statistics.userMessages++;
      } catch (error) {
        migrationResult.errors.push(`迁移用户消息 ${message.id} 时出错: ${error.message}`);
      }
    } else {
      // 检查是否为孤立的AI消息
      if (!isAIMessageLinkedToUser(message, oldMessages, i)) {
        migrationResult.statistics.orphanedAIMessages++;
        migrationResult.warnings.push(`发现孤立的AI消息: ${message.id}`);
      }
    }
  }
  
  return newMessages;
}

/**
 * 迁移单个用户消息
 * @param {Object} userMessage - 用户消息
 * @param {Array} allMessages - 所有消息数组
 * @param {number} messageIndex - 消息在数组中的索引
 * @param {Object} migrationResult - 迁移结果对象
 * @returns {Object} 迁移后的消息
 */
function migrateUserMessage(userMessage, allMessages, messageIndex, migrationResult) {
  // 创建新的消息结构
  const newMessage = {
    id: userMessage.id,
    isUser: true,
    timestamp: userMessage.timestamp,
    edited: userMessage.edited || false,
    collapsed: userMessage.collapsed || false,
    type: userMessage.type || 'text',
    versions: [],
    currentVersionIndex: userMessage.currentVersionIndex || 0,
    metadata: {
      migratedFrom: 'legacy',
      migrationTimestamp: Date.now()
    }
  };

  // 迁移版本数据
  if (userMessage.versions && Array.isArray(userMessage.versions)) {
    userMessage.versions.forEach((oldVersion, versionIndex) => {
      const newVersion = migrateMessageVersion(oldVersion, userMessage, allMessages, messageIndex, migrationResult);
      newMessage.versions.push(newVersion);
      migrationResult.statistics.migratedVersions++;
    });
  } else {
    // 如果没有版本数据，创建默认版本
    const defaultVersion = createMessageVersion(userMessage.content || '');
    
    // 查找对应的AI回复
    const aiReply = findCorrespondingAIReply(userMessage, allMessages, messageIndex);
    if (aiReply) {
      defaultVersion.aiReply = createAIReply(aiReply.content, AIReplyStatus.COMPLETED);
      migrationResult.statistics.aiReplies++;
    }
    
    newMessage.versions.push(defaultVersion);
    migrationResult.statistics.migratedVersions++;
  }

  // 确保currentVersionIndex有效
  if (newMessage.currentVersionIndex >= newMessage.versions.length) {
    newMessage.currentVersionIndex = newMessage.versions.length - 1;
    migrationResult.warnings.push(`消息 ${newMessage.id} 的版本索引已调整`);
  }

  return newMessage;
}

/**
 * 迁移消息版本
 * @param {Object} oldVersion - 旧版本数据
 * @param {Object} userMessage - 用户消息
 * @param {Array} allMessages - 所有消息数组
 * @param {number} messageIndex - 消息索引
 * @param {Object} migrationResult - 迁移结果对象
 * @returns {Object} 新版本数据
 */
function migrateMessageVersion(oldVersion, userMessage, allMessages, messageIndex, migrationResult) {
  const newVersion = {
    id: oldVersion.id || `ver_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    content: oldVersion.content || '',
    timestamp: oldVersion.timestamp || userMessage.timestamp,
    aiReply: null,
    metadata: {}
  };

  // 处理版本中的AI回复
  if (oldVersion.aiReply) {
    if (typeof oldVersion.aiReply === 'string') {
      // 旧格式：AI回复是字符串
      newVersion.aiReply = createAIReply(oldVersion.aiReply, AIReplyStatus.COMPLETED);
      migrationResult.statistics.aiReplies++;
    } else if (typeof oldVersion.aiReply === 'object') {
      // 新格式：AI回复已经是对象
      newVersion.aiReply = {
        id: oldVersion.aiReply.id || `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: oldVersion.aiReply.content || '',
        timestamp: oldVersion.aiReply.timestamp || Date.now(),
        status: oldVersion.aiReply.status || AIReplyStatus.COMPLETED,
        metadata: oldVersion.aiReply.metadata || {}
      };
      migrationResult.statistics.aiReplies++;
    }
  } else {
    // 如果版本中没有AI回复，尝试查找对应的独立AI消息
    const aiReply = findCorrespondingAIReply(userMessage, allMessages, messageIndex);
    if (aiReply) {
      newVersion.aiReply = createAIReply(aiReply.content, AIReplyStatus.COMPLETED);
      migrationResult.statistics.aiReplies++;
    }
  }

  return newVersion;
}

/**
 * 查找对应的AI回复消息
 * @param {Object} userMessage - 用户消息
 * @param {Array} allMessages - 所有消息数组
 * @param {number} userMessageIndex - 用户消息索引
 * @returns {Object|null} AI回复消息或null
 */
function findCorrespondingAIReply(userMessage, allMessages, userMessageIndex) {
  // 查找用户消息后面的第一个AI消息
  for (let i = userMessageIndex + 1; i < allMessages.length; i++) {
    const message = allMessages[i];
    
    if (!message.isUser) {
      // 找到AI消息，检查是否属于当前用户消息
      return message;
    } else {
      // 遇到下一个用户消息，停止查找
      break;
    }
  }
  
  return null;
}

/**
 * 检查AI消息是否与用户消息关联
 * @param {Object} aiMessage - AI消息
 * @param {Array} allMessages - 所有消息数组
 * @param {number} aiMessageIndex - AI消息索引
 * @returns {boolean} 是否关联
 */
function isAIMessageLinkedToUser(aiMessage, allMessages, aiMessageIndex) {
  // 向前查找最近的用户消息
  for (let i = aiMessageIndex - 1; i >= 0; i--) {
    const message = allMessages[i];
    
    if (message.isUser) {
      return true; // 找到用户消息，说明AI消息有关联
    }
  }
  
  return false; // 没有找到用户消息，说明是孤立的AI消息
}

/**
 * 验证迁移后的数据
 * @param {Array} newMessages - 新格式的消息数组
 * @returns {Object} 验证结果
 */
function validateMigratedData(newMessages) {
  const warnings = [];
  const errors = [];
  
  newMessages.forEach((message, index) => {
    const validation = validateMessage(message);
    
    if (!validation.isValid) {
      errors.push(`消息 ${index} 验证失败: ${validation.errors.join(', ')}`);
    }
    
    if (validation.warnings.length > 0) {
      warnings.push(`消息 ${index} 警告: ${validation.warnings.join(', ')}`);
    }
  });
  
  return { warnings, errors };
}

/**
 * 创建数据备份
 * @param {Array} data - 要备份的数据
 * @returns {Object} 备份信息
 */
function createBackup(data) {
  const backup = {
    timestamp: Date.now(),
    version: 'legacy',
    data: JSON.parse(JSON.stringify(data)) // 深拷贝
  };
  
  // 保存到localStorage
  try {
    localStorage.setItem('message-data-backup', JSON.stringify(backup));
    return { success: true, timestamp: backup.timestamp };
  } catch (error) {
    console.error('创建备份失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 恢复数据备份
 * @returns {Array|null} 备份的数据或null
 */
export function restoreFromBackup() {
  try {
    const backupStr = localStorage.getItem('message-data-backup');
    if (backupStr) {
      const backup = JSON.parse(backupStr);
      console.log('恢复数据备份，时间戳:', new Date(backup.timestamp));
      return backup.data;
    }
  } catch (error) {
    console.error('恢复备份失败:', error);
  }
  
  return null;
}

/**
 * 清理备份数据
 */
export function clearBackup() {
  try {
    localStorage.removeItem('message-data-backup');
    console.log('备份数据已清理');
  } catch (error) {
    console.error('清理备份失败:', error);
  }
}
