/**
 * 消息数据结构类型定义
 * 完全重构数据结构，AI回复完全内嵌在版本中
 */

/**
 * AI回复状态枚举
 */
export const AIReplyStatus = {
  PENDING: 'pending',        // 等待生成
  GENERATING: 'generating',  // 正在生成
  COMPLETED: 'completed',    // 生成完成
  ERROR: 'error',           // 生成错误
  CANCELLED: 'cancelled',   // 用户取消
  TIMEOUT: 'timeout'        // 生成超时
};

/**
 * AI回复错误类型枚举
 */
export const AIReplyErrorType = {
  NETWORK_ERROR: 'network_error',
  API_ERROR: 'api_error',
  TIMEOUT_ERROR: 'timeout_error',
  VALIDATION_ERROR: 'validation_error',
  UNKNOWN_ERROR: 'unknown_error'
};

/**
 * 消息类型枚举
 */
export const MessageType = {
  TEXT: 'text',
  IMAGE: 'image',
  FILE: 'file',
  CODE: 'code'
};

/**
 * AI回复数据结构
 * @typedef {Object} AIReply
 * @property {string} id - AI回复的唯一标识符
 * @property {string} content - AI回复内容
 * @property {number} timestamp - AI回复生成时间戳
 * @property {number} [startTimestamp] - 开始生成时间戳
 * @property {number} [endTimestamp] - 完成生成时间戳
 * @property {string} status - AI回复状态 (pending|generating|completed|error|cancelled|timeout)
 * @property {number} progress - 生成进度 (0-100)
 * @property {Object} metadata - AI回复元数据
 * @property {string} [metadata.model] - 使用的AI模型
 * @property {number} [metadata.tokens] - 消耗的token数量
 * @property {number} [metadata.inputTokens] - 输入token数量
 * @property {number} [metadata.outputTokens] - 输出token数量
 * @property {number} [metadata.duration] - 生成耗时（毫秒）
 * @property {string} [metadata.error] - 错误信息（当status为error时）
 * @property {string} [metadata.errorType] - 错误类型
 * @property {number} [metadata.retryCount] - 重试次数
 * @property {string} [metadata.requestId] - 请求ID
 * @property {Object} [metadata.apiResponse] - 原始API响应
 */

/**
 * 消息版本数据结构
 * @typedef {Object} MessageVersion
 * @property {string} id - 版本的唯一标识符
 * @property {string} content - 版本内容
 * @property {number} timestamp - 版本创建时间戳
 * @property {AIReply|null} aiReply - 该版本对应的AI回复（如果有）
 * @property {Object} metadata - 版本元数据
 * @property {string} [metadata.editReason] - 编辑原因
 * @property {boolean} [metadata.isAutoSaved] - 是否自动保存
 * @property {string} [metadata.editType] - 编辑类型 (manual|auto|correction)
 * @property {string} [metadata.previousVersionId] - 前一个版本ID
 * @property {number} [metadata.wordCount] - 字数统计
 * @property {Array} [metadata.tags] - 版本标签
 * @property {boolean} [metadata.isDeleted] - 是否已删除（软删除）
 * @property {string} [metadata.source] - 内容来源 (user|import|template)
 */

/**
 * 消息数据结构（新版本 - ）
 * @typedef {Object} Message
 * @property {string} id - 消息的唯一标识符
 * @property {boolean} isUser - 是否为用户消息（AI消息已完全移除）
 * @property {number} timestamp - 消息创建时间戳
 * @property {boolean} edited - 是否已编辑
 * @property {boolean} collapsed - 是否折叠显示
 * @property {string} type - 消息类型 (text|image|file|code)
 * @property {MessageVersion[]} versions - 消息版本数组
 * @property {number} currentVersionIndex - 当前显示的版本索引
 * @property {Object} metadata - 消息元数据（可选）
 * @property {string} [metadata.threadId] - 线程ID（用于回复功能）
 * @property {string} [metadata.parentId] - 父消息ID（用于回复功能）
 * @property {Array} [metadata.attachments] - 附件列表
 * @property {Array} [metadata.reactions] - 反应/表情
 */

/**
 * 旧版本消息数据结构（用于迁移）
 * @typedef {Object} LegacyMessage
 * @property {string} id - 消息ID
 * @property {string} content - 消息内容
 * @property {boolean} isUser - 是否为用户消息
 * @property {number} timestamp - 时间戳
 * @property {boolean} edited - 是否已编辑
 * @property {boolean} collapsed - 是否折叠
 * @property {Array} versions - 版本数组（旧格式）
 * @property {number} currentVersionIndex - 当前版本索引
 */

/**
 * 数据迁移结果
 * @typedef {Object} MigrationResult
 * @property {Message[]} messages - 迁移后的消息数组
 * @property {Object} statistics - 迁移统计信息
 * @property {number} statistics.totalMessages - 总消息数
 * @property {number} statistics.userMessages - 用户消息数
 * @property {number} statistics.aiReplies - AI回复数
 * @property {number} statistics.migratedVersions - 迁移的版本数
 * @property {Array} warnings - 迁移警告信息
 * @property {Array} errors - 迁移错误信息
 */

/**
 * 数据验证结果
 * @typedef {Object} ValidationResult
 * @property {boolean} isValid - 数据是否有效
 * @property {Array} errors - 验证错误列表
 * @property {Array} warnings - 验证警告列表
 * @property {Object} statistics - 数据统计信息
 */

/**
 * 创建新的消息版本
 * @param {string} content - 版本内容
 * @param {AIReply|null} aiReply - AI回复（可选）
 * @returns {MessageVersion} 新的消息版本
 */
export function createMessageVersion(content, aiReply = null) {
  return {
    id: generateVersionId(),
    content,
    timestamp: Date.now(),
    aiReply,
    metadata: {}
  };
}

/**
 * 创建新的AI回复
 * @param {string} content - AI回复内容
 * @param {string} status - AI回复状态
 * @param {Object} options - 可选配置
 * @returns {AIReply} 新的AI回复对象
 */
export function createAIReply(content = '', status = AIReplyStatus.PENDING, options = {}) {
  const now = Date.now();

  return {
    id: generateAIReplyId(),
    content,
    timestamp: now,
    startTimestamp: options.startTimestamp || now,
    endTimestamp: status === AIReplyStatus.COMPLETED ? now : null,
    status,
    progress: status === AIReplyStatus.COMPLETED ? 100 : 0,
    metadata: {
      model: options.model || null,
      tokens: options.tokens || 0,
      inputTokens: options.inputTokens || 0,
      outputTokens: options.outputTokens || 0,
      duration: options.duration || 0,
      error: options.error || null,
      errorType: options.errorType || null,
      retryCount: options.retryCount || 0,
      requestId: options.requestId || null,
      apiResponse: options.apiResponse || null,
      ...options.metadata
    }
  };
}

/**
 * 创建新的消息
 * @param {string} content - 消息内容
 * @param {boolean} isUser - 是否为用户消息
 * @param {string} type - 消息类型
 * @returns {Message} 新的消息对象
 */
export function createMessage(content, isUser = true, type = MessageType.TEXT) {
  const initialVersion = createMessageVersion(content);
  
  return {
    id: generateMessageId(),
    isUser,
    timestamp: Date.now(),
    edited: false,
    collapsed: false,
    type,
    versions: [initialVersion],
    currentVersionIndex: 0,
    metadata: {}
  };
}

/**
 * 生成消息ID
 * @returns {string} 唯一的消息ID
 */
function generateMessageId() {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 生成版本ID
 * @returns {string} 唯一的版本ID
 */
function generateVersionId() {
  return `ver_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 生成AI回复ID
 * @returns {string} 唯一的AI回复ID
 */
function generateAIReplyId() {
  return `ai_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 验证消息数据结构
 * @param {Message} message - 要验证的消息
 * @returns {ValidationResult} 验证结果
 */
export function validateMessage(message) {
  const errors = [];
  const warnings = [];

  // 基本字段验证
  if (!message.id || typeof message.id !== 'string') {
    errors.push('消息ID无效');
  }
  
  if (typeof message.isUser !== 'boolean') {
    errors.push('isUser字段必须是布尔值');
  }
  
  if (!message.timestamp || typeof message.timestamp !== 'number') {
    errors.push('时间戳无效');
  }
  
  if (!Array.isArray(message.versions) || message.versions.length === 0) {
    errors.push('版本数组无效或为空');
  }
  
  if (typeof message.currentVersionIndex !== 'number' || 
      message.currentVersionIndex < 0 || 
      message.currentVersionIndex >= message.versions.length) {
    errors.push('当前版本索引无效');
  }

  // 版本验证
  if (message.versions) {
    message.versions.forEach((version, index) => {
      if (!version.id || typeof version.id !== 'string') {
        errors.push(`版本${index}的ID无效`);
      }
      
      if (!version.content || typeof version.content !== 'string') {
        errors.push(`版本${index}的内容无效`);
      }
      
      if (!version.timestamp || typeof version.timestamp !== 'number') {
        errors.push(`版本${index}的时间戳无效`);
      }
      
      // AI回复验证
      if (version.aiReply) {
        const aiReply = version.aiReply;
        if (!aiReply.id || typeof aiReply.id !== 'string') {
          errors.push(`版本${index}的AI回复ID无效`);
        }

        if (typeof aiReply.content !== 'string') {
          errors.push(`版本${index}的AI回复内容无效`);
        }

        if (!Object.values(AIReplyStatus).includes(aiReply.status)) {
          errors.push(`版本${index}的AI回复状态无效`);
        }

        if (!aiReply.timestamp || typeof aiReply.timestamp !== 'number') {
          errors.push(`版本${index}的AI回复时间戳无效`);
        }

        if (typeof aiReply.progress !== 'number' || aiReply.progress < 0 || aiReply.progress > 100) {
          errors.push(`版本${index}的AI回复进度无效`);
        }

        // 验证状态和进度的一致性
        if (aiReply.status === AIReplyStatus.COMPLETED && aiReply.progress !== 100) {
          warnings.push(`版本${index}的AI回复状态为完成但进度不是100%`);
        }

        if (aiReply.status === AIReplyStatus.ERROR && !aiReply.metadata?.error) {
          warnings.push(`版本${index}的AI回复状态为错误但没有错误信息`);
        }
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    statistics: {
      versionsCount: message.versions ? message.versions.length : 0,
      aiRepliesCount: message.versions ?
        message.versions.filter(v => v.aiReply).length : 0,
      completedRepliesCount: message.versions ?
        message.versions.filter(v => v.aiReply?.status === AIReplyStatus.COMPLETED).length : 0,
      errorRepliesCount: message.versions ?
        message.versions.filter(v => v.aiReply?.status === AIReplyStatus.ERROR).length : 0
    }
  };
}

/**
 * 更新AI回复状态
 * @param {AIReply} aiReply - AI回复对象
 * @param {string} status - 新状态
 * @param {Object} updates - 其他更新字段
 * @returns {AIReply} 更新后的AI回复对象
 */
export function updateAIReplyStatus(aiReply, status, updates = {}) {
  const now = Date.now();

  const updatedReply = {
    ...aiReply,
    status,
    timestamp: now,
    ...updates
  };

  // 根据状态自动设置相关字段
  switch (status) {
    case AIReplyStatus.GENERATING:
      updatedReply.startTimestamp = updatedReply.startTimestamp || now;
      updatedReply.progress = Math.max(0, Math.min(99, updates.progress || aiReply.progress || 0));
      break;

    case AIReplyStatus.COMPLETED:
      updatedReply.endTimestamp = now;
      updatedReply.progress = 100;
      if (updatedReply.startTimestamp) {
        updatedReply.metadata.duration = now - updatedReply.startTimestamp;
      }
      break;

    case AIReplyStatus.ERROR:
    case AIReplyStatus.CANCELLED:
    case AIReplyStatus.TIMEOUT:
      updatedReply.endTimestamp = now;
      if (updatedReply.startTimestamp) {
        updatedReply.metadata.duration = now - updatedReply.startTimestamp;
      }
      break;
  }

  return updatedReply;
}

/**
 * 检查AI回复是否处于活跃状态（正在生成或等待）
 * @param {AIReply} aiReply - AI回复对象
 * @returns {boolean} 是否处于活跃状态
 */
export function isAIReplyActive(aiReply) {
  return aiReply && [
    AIReplyStatus.PENDING,
    AIReplyStatus.GENERATING
  ].includes(aiReply.status);
}

/**
 * 检查AI回复是否已完成（成功或失败）
 * @param {AIReply} aiReply - AI回复对象
 * @returns {boolean} 是否已完成
 */
export function isAIReplyFinished(aiReply) {
  return aiReply && [
    AIReplyStatus.COMPLETED,
    AIReplyStatus.ERROR,
    AIReplyStatus.CANCELLED,
    AIReplyStatus.TIMEOUT
  ].includes(aiReply.status);
}
