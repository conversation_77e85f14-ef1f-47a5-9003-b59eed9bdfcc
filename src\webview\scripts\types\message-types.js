/**
 * 消息数据结构类型定义
 * 方案B：完全重构数据结构，AI回复完全内嵌在版本中
 */

/**
 * AI回复状态枚举
 */
export const AIReplyStatus = {
  GENERATING: 'generating',
  COMPLETED: 'completed',
  ERROR: 'error',
  CANCELLED: 'cancelled'
};

/**
 * 消息类型枚举
 */
export const MessageType = {
  TEXT: 'text',
  IMAGE: 'image',
  FILE: 'file',
  CODE: 'code'
};

/**
 * AI回复数据结构
 * @typedef {Object} AIReply
 * @property {string} id - AI回复的唯一标识符
 * @property {string} content - AI回复内容
 * @property {number} timestamp - AI回复生成时间戳
 * @property {string} status - AI回复状态 (generating|completed|error|cancelled)
 * @property {Object} metadata - AI回复元数据（可选）
 * @property {string} [metadata.model] - 使用的AI模型
 * @property {number} [metadata.tokens] - 消耗的token数量
 * @property {string} [metadata.error] - 错误信息（当status为error时）
 */

/**
 * 消息版本数据结构
 * @typedef {Object} MessageVersion
 * @property {string} id - 版本的唯一标识符
 * @property {string} content - 版本内容
 * @property {number} timestamp - 版本创建时间戳
 * @property {AIReply|null} aiReply - 该版本对应的AI回复（如果有）
 * @property {Object} metadata - 版本元数据（可选）
 * @property {string} [metadata.editReason] - 编辑原因
 * @property {boolean} [metadata.isAutoSaved] - 是否自动保存
 */

/**
 * 消息数据结构（新版本 - 方案B）
 * @typedef {Object} Message
 * @property {string} id - 消息的唯一标识符
 * @property {boolean} isUser - 是否为用户消息（AI消息已完全移除）
 * @property {number} timestamp - 消息创建时间戳
 * @property {boolean} edited - 是否已编辑
 * @property {boolean} collapsed - 是否折叠显示
 * @property {string} type - 消息类型 (text|image|file|code)
 * @property {MessageVersion[]} versions - 消息版本数组
 * @property {number} currentVersionIndex - 当前显示的版本索引
 * @property {Object} metadata - 消息元数据（可选）
 * @property {string} [metadata.threadId] - 线程ID（用于回复功能）
 * @property {string} [metadata.parentId] - 父消息ID（用于回复功能）
 * @property {Array} [metadata.attachments] - 附件列表
 * @property {Array} [metadata.reactions] - 反应/表情
 */

/**
 * 旧版本消息数据结构（用于迁移）
 * @typedef {Object} LegacyMessage
 * @property {string} id - 消息ID
 * @property {string} content - 消息内容
 * @property {boolean} isUser - 是否为用户消息
 * @property {number} timestamp - 时间戳
 * @property {boolean} edited - 是否已编辑
 * @property {boolean} collapsed - 是否折叠
 * @property {Array} versions - 版本数组（旧格式）
 * @property {number} currentVersionIndex - 当前版本索引
 */

/**
 * 数据迁移结果
 * @typedef {Object} MigrationResult
 * @property {Message[]} messages - 迁移后的消息数组
 * @property {Object} statistics - 迁移统计信息
 * @property {number} statistics.totalMessages - 总消息数
 * @property {number} statistics.userMessages - 用户消息数
 * @property {number} statistics.aiReplies - AI回复数
 * @property {number} statistics.migratedVersions - 迁移的版本数
 * @property {Array} warnings - 迁移警告信息
 * @property {Array} errors - 迁移错误信息
 */

/**
 * 数据验证结果
 * @typedef {Object} ValidationResult
 * @property {boolean} isValid - 数据是否有效
 * @property {Array} errors - 验证错误列表
 * @property {Array} warnings - 验证警告列表
 * @property {Object} statistics - 数据统计信息
 */

/**
 * 创建新的消息版本
 * @param {string} content - 版本内容
 * @param {AIReply|null} aiReply - AI回复（可选）
 * @returns {MessageVersion} 新的消息版本
 */
export function createMessageVersion(content, aiReply = null) {
  return {
    id: generateVersionId(),
    content,
    timestamp: Date.now(),
    aiReply,
    metadata: {}
  };
}

/**
 * 创建新的AI回复
 * @param {string} content - AI回复内容
 * @param {string} status - AI回复状态
 * @returns {AIReply} 新的AI回复对象
 */
export function createAIReply(content, status = AIReplyStatus.COMPLETED) {
  return {
    id: generateAIReplyId(),
    content,
    timestamp: Date.now(),
    status,
    metadata: {}
  };
}

/**
 * 创建新的消息
 * @param {string} content - 消息内容
 * @param {boolean} isUser - 是否为用户消息
 * @param {string} type - 消息类型
 * @returns {Message} 新的消息对象
 */
export function createMessage(content, isUser = true, type = MessageType.TEXT) {
  const initialVersion = createMessageVersion(content);
  
  return {
    id: generateMessageId(),
    isUser,
    timestamp: Date.now(),
    edited: false,
    collapsed: false,
    type,
    versions: [initialVersion],
    currentVersionIndex: 0,
    metadata: {}
  };
}

/**
 * 生成消息ID
 * @returns {string} 唯一的消息ID
 */
function generateMessageId() {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 生成版本ID
 * @returns {string} 唯一的版本ID
 */
function generateVersionId() {
  return `ver_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 生成AI回复ID
 * @returns {string} 唯一的AI回复ID
 */
function generateAIReplyId() {
  return `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 验证消息数据结构
 * @param {Message} message - 要验证的消息
 * @returns {ValidationResult} 验证结果
 */
export function validateMessage(message) {
  const errors = [];
  const warnings = [];

  // 基本字段验证
  if (!message.id || typeof message.id !== 'string') {
    errors.push('消息ID无效');
  }
  
  if (typeof message.isUser !== 'boolean') {
    errors.push('isUser字段必须是布尔值');
  }
  
  if (!message.timestamp || typeof message.timestamp !== 'number') {
    errors.push('时间戳无效');
  }
  
  if (!Array.isArray(message.versions) || message.versions.length === 0) {
    errors.push('版本数组无效或为空');
  }
  
  if (typeof message.currentVersionIndex !== 'number' || 
      message.currentVersionIndex < 0 || 
      message.currentVersionIndex >= message.versions.length) {
    errors.push('当前版本索引无效');
  }

  // 版本验证
  if (message.versions) {
    message.versions.forEach((version, index) => {
      if (!version.id || typeof version.id !== 'string') {
        errors.push(`版本${index}的ID无效`);
      }
      
      if (!version.content || typeof version.content !== 'string') {
        errors.push(`版本${index}的内容无效`);
      }
      
      if (!version.timestamp || typeof version.timestamp !== 'number') {
        errors.push(`版本${index}的时间戳无效`);
      }
      
      // AI回复验证
      if (version.aiReply) {
        const aiReply = version.aiReply;
        if (!aiReply.id || typeof aiReply.id !== 'string') {
          errors.push(`版本${index}的AI回复ID无效`);
        }
        
        if (typeof aiReply.content !== 'string') {
          errors.push(`版本${index}的AI回复内容无效`);
        }
        
        if (!Object.values(AIReplyStatus).includes(aiReply.status)) {
          errors.push(`版本${index}的AI回复状态无效`);
        }
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    statistics: {
      versionsCount: message.versions ? message.versions.length : 0,
      aiRepliesCount: message.versions ? 
        message.versions.filter(v => v.aiReply).length : 0
    }
  };
}
