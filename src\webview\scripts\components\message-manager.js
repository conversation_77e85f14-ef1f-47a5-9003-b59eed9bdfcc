/**
 * 消息管理器
 * 提供高级的消息管理功能，包括批量操作、搜索、过滤等
 */

import { ActionCreators } from '../state/actions.js';
import { StorageUtils } from '../utils/storage.js';

/**
 * 消息数据结构定义
 */
export const MessageSchema = {
  id: 'string',           // 唯一标识符
  content: 'string',      // 消息内容
  isUser: 'boolean',      // 是否为用户消息
  timestamp: 'number',    // 时间戳
  edited: 'boolean',      // 是否已编辑
  collapsed: 'boolean',   // 是否折叠
  type: 'string',         // 消息类型 (text, image, file, etc.)
  metadata: 'object',     // 元数据
  status: 'string',       // 消息状态 (sending, sent, failed, etc.)
  reactions: 'array',     // 反应/表情
  threadId: 'string',     // 线程ID（用于回复功能）
  parentId: 'string',     // 父消息ID（用于回复功能）
  attachments: 'array'    // 附件列表
};

/**
 * 消息类型枚举
 */
export const MessageTypes = {
  TEXT: 'text',
  IMAGE: 'image',
  FILE: 'file',
  CODE: 'code',
  SYSTEM: 'system',
  ERROR: 'error'
};

/**
 * 消息状态枚举
 */
export const MessageStatus = {
  DRAFT: 'draft',
  SENDING: 'sending',
  SENT: 'sent',
  FAILED: 'failed',
  DELIVERED: 'delivered',
  READ: 'read'
};

/**
 * 消息管理器类
 */
export class MessageManager {
  constructor(store, eventManager) {
    this.store = store;
    this.eventManager = eventManager;
    this.messages = [];
    this.selectedMessages = new Set();
    this.searchIndex = new Map();
    
    // 绑定方法上下文
    this.addMessage = this.addMessage.bind(this);
    this.updateMessage = this.updateMessage.bind(this);
    this.deleteMessage = this.deleteMessage.bind(this);
    this.batchDelete = this.batchDelete.bind(this);
    this.searchMessages = this.searchMessages.bind(this);
    this.filterMessages = this.filterMessages.bind(this);
    
    // 初始化
    this.init();
  }

  /**
   * 初始化
   */
  init() {
    // 从store加载消息
    this.messages = this.store.getState('messages') || [];
    
    // 建立搜索索引
    this.buildSearchIndex();
    
    // 监听store变化
    this.store.subscribe('messages', (messages) => {
      this.messages = messages;
      this.buildSearchIndex();
    });
  }

  /**
   * 创建消息
   * @param {object} messageData - 消息数据
   * @returns {object} 标准化的消息对象
   */
  createMessage(messageData) {
    const defaultMessage = {
      id: this.generateId(),
      content: '',
      isUser: false,
      timestamp: Date.now(),
      edited: false,
      collapsed: false,
      type: MessageTypes.TEXT,
      metadata: {},
      status: MessageStatus.DRAFT,
      reactions: [],
      threadId: null,
      parentId: null,
      attachments: []
    };

    return { ...defaultMessage, ...messageData };
  }

  /**
   * 添加消息
   * @param {object} messageData - 消息数据
   * @returns {object} 添加的消息
   */
  addMessage(messageData) {
    const message = this.createMessage(messageData);
    
    // 验证消息
    if (!this.validateMessage(message)) {
      throw new Error('Invalid message data');
    }

    // 添加到列表
    this.messages.push(message);
    
    // 更新store
    this.store.dispatch(ActionCreators.addMessage(message));
    
    // 添加到搜索索引
    this.addToSearchIndex(message);
    
    // 持久化
    StorageUtils.addMessageToHistory(message);
    
    // 触发事件
    this.eventManager.emit('message:added', message);
    
    return message;
  }

  /**
   * 更新消息
   * @param {string} messageId - 消息ID
   * @param {object} updates - 更新数据
   * @returns {object|null} 更新后的消息
   */
  updateMessage(messageId, updates) {
    const messageIndex = this.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return null;

    const oldMessage = this.messages[messageIndex];
    const updatedMessage = { ...oldMessage, ...updates };
    
    // 验证更新后的消息
    if (!this.validateMessage(updatedMessage)) {
      throw new Error('Invalid message update data');
    }

    // 更新列表
    this.messages[messageIndex] = updatedMessage;
    
    // 更新store
    this.store.dispatch(ActionCreators.updateMessage(messageId, updates));
    
    // 更新搜索索引
    this.updateSearchIndex(updatedMessage);
    
    // 持久化
    StorageUtils.saveChatHistory(this.messages);
    
    // 触发事件
    this.eventManager.emit('message:updated', updatedMessage, oldMessage);
    
    return updatedMessage;
  }

  /**
   * 删除消息
   * @param {string} messageId - 消息ID
   * @returns {boolean} 是否成功删除
   */
  deleteMessage(messageId) {
    const messageIndex = this.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return false;

    const deletedMessage = this.messages.splice(messageIndex, 1)[0];
    
    // 更新store
    this.store.dispatch(ActionCreators.deleteMessage(messageId));
    
    // 从搜索索引中移除
    this.removeFromSearchIndex(deletedMessage);
    
    // 持久化
    StorageUtils.saveChatHistory(this.messages);
    
    // 触发事件
    this.eventManager.emit('message:deleted', deletedMessage);
    
    return true;
  }

  /**
   * 批量删除消息
   * @param {Array} messageIds - 消息ID数组
   * @returns {number} 删除的消息数量
   */
  batchDelete(messageIds) {
    let deletedCount = 0;
    
    messageIds.forEach(id => {
      if (this.deleteMessage(id)) {
        deletedCount++;
      }
    });
    
    this.eventManager.emit('messages:batchDeleted', messageIds, deletedCount);
    
    return deletedCount;
  }

  /**
   * 搜索消息
   * @param {string} query - 搜索查询
   * @param {object} options - 搜索选项
   * @returns {Array} 搜索结果
   */
  searchMessages(query, options = {}) {
    const {
      caseSensitive = false,
      exactMatch = false,
      includeMetadata = false,
      messageType = null,
      dateRange = null
    } = options;

    if (!query.trim()) return this.messages;

    const searchQuery = caseSensitive ? query : query.toLowerCase();
    const results = [];

    this.messages.forEach(message => {
      let searchText = message.content;
      
      if (includeMetadata && message.metadata) {
        searchText += ' ' + JSON.stringify(message.metadata);
      }
      
      if (!caseSensitive) {
        searchText = searchText.toLowerCase();
      }

      // 类型过滤
      if (messageType && message.type !== messageType) {
        return;
      }

      // 日期范围过滤
      if (dateRange) {
        const messageDate = new Date(message.timestamp);
        if (dateRange.start && messageDate < dateRange.start) return;
        if (dateRange.end && messageDate > dateRange.end) return;
      }

      // 文本匹配
      const matches = exactMatch 
        ? searchText === searchQuery
        : searchText.includes(searchQuery);

      if (matches) {
        results.push({
          message,
          relevance: this.calculateRelevance(message, query)
        });
      }
    });

    // 按相关性排序
    return results
      .sort((a, b) => b.relevance - a.relevance)
      .map(result => result.message);
  }

  /**
   * 过滤消息
   * @param {function} predicate - 过滤函数
   * @returns {Array} 过滤结果
   */
  filterMessages(predicate) {
    return this.messages.filter(predicate);
  }

  /**
   * 获取用户消息
   * @returns {Array} 用户消息列表
   */
  getUserMessages() {
    return this.filterMessages(message => message.isUser);
  }

  /**
   * 获取AI消息
   * @returns {Array} AI消息列表
   */
  getAIMessages() {
    return this.filterMessages(message => !message.isUser);
  }

  /**
   * 获取消息统计
   * @returns {object} 统计信息
   */
  getStatistics() {
    const total = this.messages.length;
    const userMessages = this.getUserMessages().length;
    const aiMessages = this.getAIMessages().length;
    const editedMessages = this.filterMessages(m => m.edited).length;
    
    const typeStats = {};
    Object.values(MessageTypes).forEach(type => {
      typeStats[type] = this.filterMessages(m => m.type === type).length;
    });

    return {
      total,
      userMessages,
      aiMessages,
      editedMessages,
      typeStats,
      averageLength: this.calculateAverageLength(),
      oldestMessage: this.getOldestMessage(),
      newestMessage: this.getNewestMessage()
    };
  }

  /**
   * 验证消息
   * @param {object} message - 消息对象
   * @returns {boolean} 是否有效
   */
  validateMessage(message) {
    if (!message || typeof message !== 'object') return false;
    if (!message.id || typeof message.id !== 'string') return false;
    if (!message.content || typeof message.content !== 'string') return false;
    if (typeof message.isUser !== 'boolean') return false;
    if (!message.timestamp || typeof message.timestamp !== 'number') return false;
    
    return true;
  }

  /**
   * 建立搜索索引
   */
  buildSearchIndex() {
    this.searchIndex.clear();
    
    this.messages.forEach(message => {
      this.addToSearchIndex(message);
    });
  }

  /**
   * 添加到搜索索引
   * @param {object} message - 消息对象
   */
  addToSearchIndex(message) {
    const words = this.extractWords(message.content);
    
    words.forEach(word => {
      if (!this.searchIndex.has(word)) {
        this.searchIndex.set(word, new Set());
      }
      this.searchIndex.get(word).add(message.id);
    });
  }

  /**
   * 更新搜索索引
   * @param {object} message - 消息对象
   */
  updateSearchIndex(message) {
    // 先移除旧索引
    this.removeFromSearchIndex(message);
    // 再添加新索引
    this.addToSearchIndex(message);
  }

  /**
   * 从搜索索引中移除
   * @param {object} message - 消息对象
   */
  removeFromSearchIndex(message) {
    this.searchIndex.forEach((messageIds, word) => {
      messageIds.delete(message.id);
      if (messageIds.size === 0) {
        this.searchIndex.delete(word);
      }
    });
  }

  /**
   * 提取单词
   * @param {string} text - 文本
   * @returns {Array} 单词数组
   */
  extractWords(text) {
    return text.toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 1);
  }

  /**
   * 计算相关性
   * @param {object} message - 消息对象
   * @param {string} query - 查询字符串
   * @returns {number} 相关性分数
   */
  calculateRelevance(message, query) {
    const content = message.content.toLowerCase();
    const queryLower = query.toLowerCase();
    
    let score = 0;
    
    // 完全匹配得分最高
    if (content === queryLower) score += 100;
    
    // 包含查询字符串
    if (content.includes(queryLower)) score += 50;
    
    // 单词匹配
    const queryWords = this.extractWords(query);
    const contentWords = this.extractWords(content);
    
    queryWords.forEach(word => {
      if (contentWords.includes(word)) score += 10;
    });
    
    // 时间因子（新消息得分更高）
    const timeFactor = (Date.now() - message.timestamp) / (1000 * 60 * 60 * 24); // 天数
    score += Math.max(0, 10 - timeFactor);
    
    return score;
  }

  /**
   * 计算平均长度
   * @returns {number} 平均长度
   */
  calculateAverageLength() {
    if (this.messages.length === 0) return 0;
    
    const totalLength = this.messages.reduce((sum, message) => {
      return sum + message.content.length;
    }, 0);
    
    return Math.round(totalLength / this.messages.length);
  }

  /**
   * 获取最旧的消息
   * @returns {object|null} 最旧的消息
   */
  getOldestMessage() {
    if (this.messages.length === 0) return null;
    
    return this.messages.reduce((oldest, message) => {
      return message.timestamp < oldest.timestamp ? message : oldest;
    });
  }

  /**
   * 获取最新的消息
   * @returns {object|null} 最新的消息
   */
  getNewestMessage() {
    if (this.messages.length === 0) return null;
    
    return this.messages.reduce((newest, message) => {
      return message.timestamp > newest.timestamp ? message : newest;
    });
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 清空所有消息
   */
  clear() {
    this.messages = [];
    this.selectedMessages.clear();
    this.searchIndex.clear();
    
    this.store.dispatch(ActionCreators.clearMessages());
    StorageUtils.clearChatHistory();
    
    this.eventManager.emit('messages:cleared');
  }

  /**
   * 导出消息
   * @param {string} format - 导出格式 (json, txt, csv)
   * @returns {string} 导出的数据
   */
  exportMessages(format = 'json') {
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(this.messages, null, 2);
      
      case 'txt':
        return this.messages.map(message => {
          const time = new Date(message.timestamp).toLocaleString();
          const sender = message.isUser ? '用户' : 'AI';
          return `[${time}] ${sender}: ${message.content}`;
        }).join('\n');
      
      case 'csv':
        const headers = 'ID,时间,发送者,内容,类型,状态';
        const rows = this.messages.map(message => {
          const time = new Date(message.timestamp).toLocaleString();
          const sender = message.isUser ? '用户' : 'AI';
          const content = message.content.replace(/"/g, '""');
          return `"${message.id}","${time}","${sender}","${content}","${message.type}","${message.status}"`;
        });
        return [headers, ...rows].join('\n');
      
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }
}

export default MessageManager;
